import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import '../models/preprocessing_options.dart';
import '../screens/project/models/video_track_model.dart';
import '../controllers/video_controller.dart';
import 'easy_video_editor_service.dart';

/// Mixin to add easy_video_editor capabilities to VideoEditorProvider
/// Uses only easy_video_editor for all video operations (no FFmpeg hybrid)
mixin EasyVideoEditorMixin on ChangeNotifier {
  
  // Abstract getters that must be implemented by the VideoEditorProvider
  List<VideoTrackModel> get videoTracks;
  void updateVideoTrack(int index, VideoTrackModel track);
  VideoEditorController? getVideoControllerForTrack(String trackId);
  Size get canvasSize;

  /// Apply video editing using easy_video_editor
  Future<void> editVideoTrack({
    required String trackId,
    required PreprocessingOptions options,
  }) async {
    final trackIndex = videoTracks.indexWhere((t) => t.id == trackId);
    if (trackIndex == -1) return;

    final track = videoTracks[trackIndex];
    
    // Start processing indicator
    updateVideoTrack(trackIndex, track.copyWith(
      isPreprocessing: true,
      preprocessingProgress: 0.0,
    ));

    try {
      // Process with easy_video_editor
      final processedFile = await EasyVideoEditorService.applySingleEdit(
        inputFile: track.activeFile,
        options: options,
        onProgress: (progress) {
          final updatedTrack = videoTracks[trackIndex].copyWith(
            preprocessingProgress: progress,
          );
          updateVideoTrack(trackIndex, updatedTrack);
        },
      );

      // Create new video controller with processed file
      final newController = await _createUpdatedVideoController(trackId, processedFile);
      await updateVideoControllerForTrack(trackId, newController);

      // Update track model
      final finalTrack = track.copyWith(
        processedFile: processedFile,
        appliedPreprocessing: options,
        isPreprocessing: false,
        preprocessingProgress: 1.0,
        // Reset canvas crop if crop was applied in processing
        canvasCropModel: options.cropOptions != null 
            ? CropModel(enabled: false)
            : track.canvasCropModel,
      );
      
      updateVideoTrack(trackIndex, finalTrack);
      
    } catch (e) {
      // Handle processing error
      final errorTrack = track.copyWith(
        isPreprocessing: false,
        preprocessingProgress: 0.0,
      );
      updateVideoTrack(trackIndex, errorTrack);
      rethrow;
    }
  }

  /// Replace video controller file while maintaining playback state
  Future<VideoEditorController> _createUpdatedVideoController(
    String trackId,
    File newFile
  ) async {
    final newController = VideoEditorController.file(newFile);
    await newController.initialize();
    return newController;
  }

  /// Abstract method to be implemented by VideoEditorProvider
  Future<void> updateVideoControllerForTrack(String trackId, VideoEditorController newController);

  /// Apply crop using easy_video_editor
  Future<void> applyCropToTrack({
    required String trackId,
    required CropModel cropModel,
  }) async {
    if (!cropModel.enabled || !cropModel.isValid) return;

    final cropOptions = CropOptions.fromCropModel(cropModel);
    final preprocessingOptions = PreprocessingOptions(
      cropOptions: cropOptions,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply trim using easy_video_editor
  Future<void> applyTrimToTrack({
    required String trackId,
    required double startSeconds,
    required double endSeconds,
  }) async {
    final trimOptions = TrimOptions(
      startSeconds: startSeconds,
      endSeconds: endSeconds,
    );
    final preprocessingOptions = PreprocessingOptions(
      trimOptions: trimOptions,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply rotation using easy_video_editor
  Future<void> applyRotationToTrack({
    required String trackId,
    required int degrees,
  }) async {
    final preprocessingOptions = PreprocessingOptions(
      rotationDegrees: degrees,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply speed adjustment using easy_video_editor
  Future<void> applySpeedToTrack({
    required String trackId,
    required double speedFactor,
  }) async {
    final preprocessingOptions = PreprocessingOptions(
      speedFactor: speedFactor,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply compression using easy_video_editor
  Future<void> applyCompressionToTrack({
    required String trackId,
    required VideoQuality quality,
    String? resolution,
  }) async {
    final compressionOptions = CompressionOptions(
      quality: quality,
      resolution: resolution,
    );
    final preprocessingOptions = PreprocessingOptions(
      compressionOptions: compressionOptions,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply audio processing using easy_video_editor
  Future<void> applyAudioProcessingToTrack({
    required String trackId,
    bool mute = false,
    bool extractAudio = false,
  }) async {
    final audioOptions = AudioOptions(
      mute: mute,
      extractAudio: extractAudio,
    );
    final preprocessingOptions = PreprocessingOptions(
      audioOptions: audioOptions,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Export entire project using easy_video_editor
  Future<File> exportProject({
    required String outputPath,
    Function(double)? onProgress,
  }) async {
    return await EasyVideoEditorService.createCanvasComposition(
      videoTracks: videoTracks,
      textTracks: [], // Add your text tracks here
      audioTracks: [], // Add your audio tracks here
      canvasSize: canvasSize,
      onProgress: onProgress,
    );
  }

  /// Get track by ID
  VideoTrackModel? getTrackById(String trackId) {
    try {
      return videoTracks.firstWhere((track) => track.id == trackId);
    } catch (e) {
      return null;
    }
  }

  /// Check if any tracks are currently processing
  bool get hasProcessingTracks {
    return videoTracks.any((track) => track.isPreprocessing);
  }

  /// Get processing progress for UI display
  double get overallProcessingProgress {
    final processingTracks = videoTracks.where((track) => track.isPreprocessing).toList();
    if (processingTracks.isEmpty) return 1.0;
    
    final totalProgress = processingTracks.fold<double>(
      0.0, 
      (sum, track) => sum + track.preprocessingProgress
    );
    
    return totalProgress / processingTracks.length;
  }

  /// Get processing status message for UI
  String get processingStatusMessage {
    final processingTracks = videoTracks.where((track) => track.isPreprocessing).toList();
    if (processingTracks.isEmpty) return '';
    
    if (processingTracks.length == 1) {
      return 'Processing video with easy_video_editor...';
    } else {
      return 'Processing ${processingTracks.length} videos with easy_video_editor...';
    }
  }

  /// Clear all processing data
  Future<void> clearProcessingData() async {
    // Clear service cache
    await EasyVideoEditorService.clearOutputFiles();
    
    // Reset all tracks processing state
    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];
      if (track.isPreprocessed || track.isPreprocessing) {
        updateVideoTrack(i, track.copyWith(
          preprocessedFile: null,
          appliedPreprocessing: null,
          isPreprocessing: false,
          preprocessingProgress: 0.0,
        ));
      }
    }
  }

  /// Generate thumbnail for track
  Future<File?> generateThumbnailForTrack({
    required String trackId,
    double positionMs = 1000,
    int? width,
    int? height,
  }) async {
    final track = getTrackById(trackId);
    if (track == null) return null;

    return await EasyVideoEditorService.generateThumbnail(
      videoFile: track.activeFile,
      positionMs: positionMs,
      width: width,
      height: height,
    );
  }

  /// Get metadata for track
  Future<Map<String, dynamic>?> getTrackMetadata(String trackId) async {
    final track = getTrackById(trackId);
    if (track == null) return null;

    return await EasyVideoEditorService.getVideoMetadata(track.activeFile);
  }

  /// Apply multiple operations in sequence
  Future<void> applyMultipleOperationsToTrack({
    required String trackId,
    TrimOptions? trimOptions,
    CropOptions? cropOptions,
    int? rotationDegrees,
    double? speedFactor,
    CompressionOptions? compressionOptions,
    AudioOptions? audioOptions,
  }) async {
    final preprocessingOptions = PreprocessingOptions(
      trimOptions: trimOptions,
      cropOptions: cropOptions,
      rotationDegrees: rotationDegrees ?? 0,
      speedFactor: speedFactor ?? 1.0,
      compressionOptions: compressionOptions,
      audioOptions: audioOptions,
    );

    await editVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }
}