import 'dart:io';
import 'dart:math' as math;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:easy_video_editor/easy_video_editor.dart';
import '../models/preprocessing_options.dart';

/// Service for preprocessing videos using easy_video_editor
/// Integrates with your existing VideoCap architecture
class VideoPreprocessingService {
  static const String _cacheDir = 'preprocessed_videos';
  
  /// Preprocess video with easy_video_editor
  /// Returns the preprocessed file or original if no preprocessing needed
  static Future<File> preprocessVideo({
    required File originalFile,
    PreprocessingOptions? options,
    Function(double)? onProgress,
  }) async {
    if (options == null || !options.hasAnyEdit) {
      return originalFile; // No preprocessing needed
    }

    final outputFile = await _generateOutputFile(originalFile);
    
    try {
      File currentFile = originalFile;
      // Progress tracking will be handled by individual operations
      final totalSteps = _countProcessingSteps(options);
      int currentStep = 0;
      
      // Apply edits in sequence for best quality
      if (options.trimOptions != null) {
        currentFile = await _applyTrim(
          currentFile, 
          options.trimOptions!, 
          (progress) {
            final stepProgress = (currentStep + progress) / totalSteps;
            onProgress?.call(stepProgress);
          }
        );
        currentStep++;
      }
      
      if (options.cropOptions != null) {
        currentFile = await _applyCrop(
          currentFile, 
          options.cropOptions!, 
          (progress) {
            final stepProgress = (currentStep + progress) / totalSteps;
            onProgress?.call(stepProgress);
          }
        );
        currentStep++;
      }
      
      if (options.rotationDegrees != 0) {
        currentFile = await _applyRotation(
          currentFile, 
          options.rotationDegrees, 
          (progress) {
            final stepProgress = (currentStep + progress) / totalSteps;
            onProgress?.call(stepProgress);
          }
        );
        currentStep++;
      }
      
      if (options.speedFactor != 1.0) {
        currentFile = await _applySpeed(
          currentFile, 
          options.speedFactor, 
          (progress) {
            final stepProgress = (currentStep + progress) / totalSteps;
            onProgress?.call(stepProgress);
          }
        );
        currentStep++;
      }
      
      if (options.compressionOptions != null) {
        currentFile = await _applyCompression(
          currentFile, 
          options.compressionOptions!, 
          (progress) {
            final stepProgress = (currentStep + progress) / totalSteps;
            onProgress?.call(stepProgress);
          }
        );
        currentStep++;
      }
      
      if (options.audioOptions != null) {
        currentFile = await _applyAudioEdit(
          currentFile, 
          options.audioOptions!, 
          (progress) {
            final stepProgress = (currentStep + progress) / totalSteps;
            onProgress?.call(stepProgress);
          }
        );
        currentStep++;
      }
      
      // Move final result to output location
      if (currentFile.path != outputFile.path) {
        await currentFile.copy(outputFile.path);
        if (currentFile.path != originalFile.path) {
          await currentFile.delete(); // Clean up intermediate files
        }
      }
      
      onProgress?.call(1.0); // Complete
      return outputFile;
      
    } catch (e) {
      // Clean up on error
      if (await outputFile.exists()) {
        await outputFile.delete();
      }
      rethrow;
    }
  }
  
  static int _countProcessingSteps(PreprocessingOptions options) {
    int steps = 0;
    if (options.trimOptions != null) steps++;
    if (options.cropOptions != null) steps++;
    if (options.rotationDegrees != 0) steps++;
    if (options.speedFactor != 1.0) steps++;
    if (options.compressionOptions != null) steps++;
    if (options.audioOptions != null) steps++;
    return math.max(steps, 1);
  }
  
  // Video processing implementations using easy_video_editor package
  // Each method includes fallback error handling to ensure robustness
  
  static Future<File> _applyTrim(
    File inputFile, 
    TrimOptions options, 
    Function(double)? onProgress
  ) async {
    final outputFile = await _generateTempFile(inputFile, 'trim');
    
    try {
      final editor = VideoEditorBuilder(videoPath: inputFile.path)
        .trim(
          startTimeMs: (options.startSeconds * 1000).round(),
          endTimeMs: (options.endSeconds * 1000).round(),
        );
      
      final processedPath = await editor.export(
        outputPath: outputFile.path,
      );
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('Trim processing failed, using original: $e');
    }
    
    return outputFile;
  }
  
  static Future<File> _applyCrop(
    File inputFile, 
    CropOptions options, 
    Function(double)? onProgress
  ) async {
    final outputFile = await _generateTempFile(inputFile, 'crop');
    
    try {
      // Map crop dimensions to aspect ratio
      final aspectRatio = _mapCropToAspectRatio(options);
      
      final editor = VideoEditorBuilder(videoPath: inputFile.path)
        .crop(aspectRatio: aspectRatio);
      
      final processedPath = await editor.export(
        outputPath: outputFile.path,
      );
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('Crop processing failed, using original: $e');
    }
    
    return outputFile;
  }
  
  static Future<File> _applyRotation(
    File inputFile, 
    int degrees, 
    Function(double)? onProgress
  ) async {
    final outputFile = await _generateTempFile(inputFile, 'rotate');
    
    try {
      final rotationDegree = _mapDegreesToRotation(degrees);
      
      final editor = VideoEditorBuilder(videoPath: inputFile.path)
        .rotate(degree: rotationDegree);
      
      final processedPath = await editor.export(
        outputPath: outputFile.path,
      );
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('Rotation processing failed, using original: $e');
    }
    
    return outputFile;
  }
  
  static Future<File> _applySpeed(
    File inputFile, 
    double speedFactor, 
    Function(double)? onProgress
  ) async {
    final outputFile = await _generateTempFile(inputFile, 'speed');
    
    try {
      final editor = VideoEditorBuilder(videoPath: inputFile.path)
        .speed(speed: speedFactor);
      
      final processedPath = await editor.export(
        outputPath: outputFile.path,
      );
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('Speed processing failed, using original: $e');
    }
    
    return outputFile;
  }
  
  static Future<File> _applyCompression(
    File inputFile, 
    CompressionOptions options, 
    Function(double)? onProgress
  ) async {
    final outputFile = await _generateTempFile(inputFile, 'compress');
    
    try {
      final resolution = _mapQualityToVideoResolution(options.quality, options.resolution);
      
      final editor = VideoEditorBuilder(videoPath: inputFile.path)
        .compress(resolution: resolution);
      
      final processedPath = await editor.export(
        outputPath: outputFile.path,
      );
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('Compression processing failed, using original: $e');
    }
    
    return outputFile;
  }
  
  static Future<File> _applyAudioEdit(
    File inputFile, 
    AudioOptions options, 
    Function(double)? onProgress
  ) async {
    final outputFile = await _generateTempFile(inputFile, 'audio');
    
    try {
      if (options.mute) {
        final editor = VideoEditorBuilder(videoPath: inputFile.path)
          .removeAudio();
        
        final processedPath = await editor.export(
          outputPath: outputFile.path,
        );
        
        if (processedPath != null && processedPath != outputFile.path) {
          final processedFile = File(processedPath);
          if (await processedFile.exists()) {
            await processedFile.copy(outputFile.path);
            await processedFile.delete();
          }
        }
        
      } else if (options.extractAudio) {
        final editor = VideoEditorBuilder(videoPath: inputFile.path);
        
        // Extract audio to separate file
        await editor.extractAudio();
        
        // Copy original video as-is for canvas
        await inputFile.copy(outputFile.path);
        onProgress?.call(1.0);
        
      } else {
        // No audio processing needed
        await inputFile.copy(outputFile.path);
        onProgress?.call(1.0);
      }
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('Audio processing failed, using original: $e');
    }
    
    return outputFile;
  }
  
  /// Map crop dimensions to video aspect ratio
  static VideoAspectRatio _mapCropToAspectRatio(CropOptions options) {
    final aspectRatio = options.width / options.height;
    
    // Match closest standard aspect ratio
    if ((aspectRatio - 16/9).abs() < 0.1) return VideoAspectRatio.ratio16x9;
    if ((aspectRatio - 4/3).abs() < 0.1) return VideoAspectRatio.ratio4x3;
    if ((aspectRatio - 1/1).abs() < 0.1) return VideoAspectRatio.ratio1x1;
    if ((aspectRatio - 9/16).abs() < 0.1) return VideoAspectRatio.ratio9x16;
    
    // Default to 16:9 for other ratios
    return VideoAspectRatio.ratio16x9;
  }
  
  /// Map degrees to rotation enum
  static RotationDegree _mapDegreesToRotation(int degrees) {
    final normalizedDegrees = degrees % 360;
    
    switch (normalizedDegrees) {
      case 90:
        return RotationDegree.degree90;
      case 180:
        return RotationDegree.degree180;
      case 270:
        return RotationDegree.degree270;
      default:
        return RotationDegree.degree90; // Default to 90 degrees
    }
  }
  
  /// Map quality and resolution to VideoResolution enum
  static VideoResolution _mapQualityToVideoResolution(VideoQuality quality, String? resolution) {
    // If specific resolution is provided, try to match it
    if (resolution != null) {
      switch (resolution.toLowerCase()) {
        case '360x640':
        case '640x360':
          return VideoResolution.p360;
        case '480x854':
        case '854x480':
          return VideoResolution.p480;
        case '720x1280':
        case '1280x720':
          return VideoResolution.p720;
        case '1080x1920':
        case '1920x1080':
          return VideoResolution.p1080;
        case '2160x3840':
        case '3840x2160':
          return VideoResolution.p1080; // Fallback to 1080p for 4K
      }
    }
    
    // Map quality to standard resolutions
    switch (quality) {
      case VideoQuality.low:
        return VideoResolution.p360;
      case VideoQuality.medium:
        return VideoResolution.p480;
      case VideoQuality.high:
        return VideoResolution.p720;
      case VideoQuality.veryHigh:
        return VideoResolution.p1080;
    }
  }
  
  static Future<File> _generateOutputFile(File originalFile) async {
    final directory = await _getCacheDirectory();
    final fileName = '${const Uuid().v4()}_processed.mp4';
    return File('${directory.path}/$fileName');
  }
  
  static Future<File> _generateTempFile(File originalFile, String suffix) async {
    final directory = await _getCacheDirectory();
    final fileName = '${const Uuid().v4()}_$suffix.mp4';
    return File('${directory.path}/$fileName');
  }
  
  static Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/$_cacheDir');
    
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    
    return cacheDir;
  }
  
  /// Clean up preprocessed files
  static Future<void> clearPreprocessedFiles() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      print('Error clearing preprocessed files: $e');
    }
  }
  
  /// Get size of preprocessed files cache
  static Future<int> getCacheSize() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) return 0;
      
      int totalSize = 0;
      await for (var entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      print('Error calculating cache size: $e');
      return 0;
    }
  }
}