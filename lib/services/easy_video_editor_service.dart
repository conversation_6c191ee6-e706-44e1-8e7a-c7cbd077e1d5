import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:easy_video_editor/easy_video_editor.dart';
import '../models/preprocessing_options.dart';
import '../screens/project/models/video_track_model.dart';
import '../screens/project/models/text_track_model.dart';
import '../screens/project/models/audio_track_model.dart';

/// Complete video editing service using only easy_video_editor
/// Replaces hybrid approach with pure easy_video_editor implementation
class EasyVideoEditorService {
  static const String _outputDir = 'easy_video_outputs';
  
  /// Apply single video edit using easy_video_editor
  static Future<File> applySingleEdit({
    required File inputFile,
    PreprocessingOptions? options,
    Function(double)? onProgress,
  }) async {
    if (options == null || !options.hasAnyEdit) {
      return inputFile; // No edits needed
    }

    final outputFile = await _generateOutputFile(inputFile, 'edited');
    
    try {
      var editor = VideoEditorBuilder(videoPath: inputFile.path);
      
      // Chain all operations
      if (options.trimOptions != null) {
        editor = editor.trim(
          startTimeMs: (options.trimOptions!.startSeconds * 1000).round(),
          endTimeMs: (options.trimOptions!.endSeconds * 1000).round(),
        );
      }
      
      if (options.cropOptions != null) {
        final aspectRatio = _mapCropToAspectRatio(options.cropOptions!);
        editor = editor.crop(aspectRatio: aspectRatio);
      }
      
      if (options.rotationDegrees != 0) {
        final rotation = _mapDegreesToRotation(options.rotationDegrees);
        editor = editor.rotate(degree: rotation);
      }
      
      if (options.speedFactor != 1.0) {
        editor = editor.speed(speed: options.speedFactor);
      }
      
      if (options.compressionOptions != null) {
        final resolution = _mapQualityToVideoResolution(
          options.compressionOptions!.quality,
          options.compressionOptions!.resolution,
        );
        editor = editor.compress(resolution: resolution);
      }
      
      if (options.audioOptions?.mute == true) {
        editor = editor.removeAudio();
      }
      
      // Execute all operations
      final processedPath = await editor.export(outputPath: outputFile.path);
      onProgress?.call(1.0);
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
      return outputFile;
      
    } catch (e) {
      // Fallback: copy original file if processing fails
      await inputFile.copy(outputFile.path);
      onProgress?.call(1.0);
      print('easy_video_editor processing failed, using original: $e');
      return outputFile;
    }
  }
  
  /// Merge multiple videos into single output
  static Future<File> mergeVideos({
    required List<File> videoFiles,
    Size? outputSize,
    Function(double)? onProgress,
  }) async {
    if (videoFiles.isEmpty) throw ArgumentError('No video files provided');
    if (videoFiles.length == 1) return videoFiles.first;
    
    final outputFile = await _generateOutputFile(videoFiles.first, 'merged');
    
    try {
      final primaryVideo = videoFiles.first;
      final otherVideos = videoFiles.skip(1).map((f) => f.path).toList();
      
      var editor = VideoEditorBuilder(videoPath: primaryVideo.path);
      
      if (otherVideos.isNotEmpty) {
        editor = editor.merge(otherVideoPaths: otherVideos);
      }
      
      // Apply compression if output size specified
      if (outputSize != null) {
        final resolution = _mapSizeToResolution(outputSize);
        editor = editor.compress(resolution: resolution);
      }
      
      final processedPath = await editor.export(outputPath: outputFile.path);
      onProgress?.call(1.0);
      
      if (processedPath != null && processedPath != outputFile.path) {
        final processedFile = File(processedPath);
        if (await processedFile.exists()) {
          await processedFile.copy(outputFile.path);
          await processedFile.delete();
        }
      }
      
      return outputFile;
      
    } catch (e) {
      print('Video merging failed: $e');
      rethrow;
    }
  }
  
  /// Create canvas-based video composition using easy_video_editor
  static Future<File> createCanvasComposition({
    required List<VideoTrackModel> videoTracks,
    required List<TextTrackModel> textTracks,
    required List<AudioTrackModel> audioTracks,
    required Size canvasSize,
    Function(double)? onProgress,
  }) async {
    if (videoTracks.isEmpty) {
      throw ArgumentError('No video tracks provided');
    }
    
    final outputFile = await _generateOutputFile(videoTracks.first.activeFile, 'composition');
    
    try {
      // Step 1: Process individual tracks if needed
      final processedTracks = <File>[];
      double totalProgress = 0.0;
      
      for (int i = 0; i < videoTracks.length; i++) {
        final track = videoTracks[i];
        
        // Apply track-specific edits
        final editOptions = PreprocessingOptions(
          trimOptions: (track.videoTrimStart > 0 || track.videoTrimEnd < track.originalDuration) ? TrimOptions(
            startSeconds: track.videoTrimStart,
            endSeconds: track.videoTrimEnd,
          ) : null,
          cropOptions: track.hasCrop ? CropOptions.fromCropModel(track.canvasCropModel!) : null,
          rotationDegrees: track.canvasRotation,
          speedFactor: 1.0, // Default speed, can be made configurable
        );
        
        final processedFile = await applySingleEdit(
          inputFile: track.activeFile,
          options: editOptions,
          onProgress: (progress) {
            final trackProgress = (i + progress) / videoTracks.length * 0.7; // 70% for processing
            onProgress?.call(trackProgress);
          },
        );
        
        processedTracks.add(processedFile);
        totalProgress += 0.7 / videoTracks.length;
      }
      
      // Step 2: Merge all tracks
      final mergedFile = await mergeVideos(
        videoFiles: processedTracks,
        outputSize: canvasSize,
        onProgress: (progress) {
          onProgress?.call(totalProgress + progress * 0.2); // 20% for merging
        },
      );
      
      // Step 3: Apply final composition settings
      var finalEditor = VideoEditorBuilder(videoPath: mergedFile.path);
      
      // Apply canvas size compression
      final resolution = _mapSizeToResolution(canvasSize);
      finalEditor = finalEditor.compress(resolution: resolution);
      
      final finalPath = await finalEditor.export(outputPath: outputFile.path);
      onProgress?.call(1.0);
      
      if (finalPath != null && finalPath != outputFile.path) {
        final finalFile = File(finalPath);
        if (await finalFile.exists()) {
          await finalFile.copy(outputFile.path);
          await finalFile.delete();
        }
      }
      
      // Clean up processed files
      for (final file in processedTracks) {
        if (file.path != videoTracks.first.activeFile.path && await file.exists()) {
          await file.delete();
        }
      }
      if (mergedFile.path != outputFile.path && await mergedFile.exists()) {
        await mergedFile.delete();
      }
      
      return outputFile;
      
    } catch (e) {
      print('Canvas composition failed: $e');
      rethrow;
    }
  }
  
  /// Apply crop operation using easy_video_editor
  static Future<File> applyCrop({
    required File inputFile,
    required CropModel cropModel,
    Function(double)? onProgress,
  }) async {
    if (!cropModel.enabled || !cropModel.isValid) {
      return inputFile;
    }
    
    final cropOptions = CropOptions.fromCropModel(cropModel);
    final preprocessingOptions = PreprocessingOptions(cropOptions: cropOptions);
    
    return await applySingleEdit(
      inputFile: inputFile,
      options: preprocessingOptions,
      onProgress: onProgress,
    );
  }
  
  /// Apply trim operation using easy_video_editor
  static Future<File> applyTrim({
    required File inputFile,
    required double startSeconds,
    required double endSeconds,
    Function(double)? onProgress,
  }) async {
    final trimOptions = TrimOptions(
      startSeconds: startSeconds,
      endSeconds: endSeconds,
    );
    final preprocessingOptions = PreprocessingOptions(trimOptions: trimOptions);
    
    return await applySingleEdit(
      inputFile: inputFile,
      options: preprocessingOptions,
      onProgress: onProgress,
    );
  }
  
  /// Generate thumbnail using easy_video_editor
  static Future<File?> generateThumbnail({
    required File videoFile,
    double positionMs = 1000,
    int? width,
    int? height,
  }) async {
    try {
      final editor = VideoEditorBuilder(videoPath: videoFile.path);
      final thumbnailPath = await editor.generateThumbnail(
        positionMs: positionMs.round(),
        quality: 80,
        width: width,
        height: height,
      );
      
      return thumbnailPath != null ? File(thumbnailPath) : null;
    } catch (e) {
      print('Thumbnail generation failed: $e');
      return null;
    }
  }
  
  /// Get video metadata using easy_video_editor
  static Future<Map<String, dynamic>?> getVideoMetadata(File videoFile) async {
    try {
      final editor = VideoEditorBuilder(videoPath: videoFile.path);
      final metadata = await editor.getVideoMetadata();
      // Convert VideoMetadata to Map with available properties
      return {
        'duration': metadata.duration,
        'width': metadata.width,
        'height': metadata.height,
        'filePath': videoFile.path,
        'fileSize': await videoFile.length(),
      };
    } catch (e) {
      print('Metadata extraction failed: $e');
      return null;
    }
  }
  
  // Helper methods
  static VideoAspectRatio _mapCropToAspectRatio(CropOptions options) {
    final aspectRatio = options.width / options.height;
    
    if ((aspectRatio - 16/9).abs() < 0.1) return VideoAspectRatio.ratio16x9;
    if ((aspectRatio - 4/3).abs() < 0.1) return VideoAspectRatio.ratio4x3;
    if ((aspectRatio - 1/1).abs() < 0.1) return VideoAspectRatio.ratio1x1;
    if ((aspectRatio - 9/16).abs() < 0.1) return VideoAspectRatio.ratio9x16;
    
    return VideoAspectRatio.ratio16x9; // Default
  }
  
  static RotationDegree _mapDegreesToRotation(int degrees) {
    final normalizedDegrees = degrees % 360;
    
    switch (normalizedDegrees) {
      case 90:
        return RotationDegree.degree90;
      case 180:
        return RotationDegree.degree180;
      case 270:
        return RotationDegree.degree270;
      default:
        return RotationDegree.degree90;
    }
  }
  
  static VideoResolution _mapQualityToVideoResolution(VideoQuality quality, String? resolution) {
    // If specific resolution is provided, try to match it
    if (resolution != null) {
      switch (resolution.toLowerCase()) {
        case '360x640':
        case '640x360':
          return VideoResolution.p360;
        case '480x854':
        case '854x480':
          return VideoResolution.p480;
        case '720x1280':
        case '1280x720':
          return VideoResolution.p720;
        case '1080x1920':
        case '1920x1080':
          return VideoResolution.p1080;
      }
    }
    
    // Map quality to standard resolutions
    switch (quality) {
      case VideoQuality.low:
        return VideoResolution.p360;
      case VideoQuality.medium:
        return VideoResolution.p480;
      case VideoQuality.high:
        return VideoResolution.p720;
      case VideoQuality.veryHigh:
        return VideoResolution.p1080;
    }
  }
  
  static VideoResolution _mapSizeToResolution(Size size) {
    final maxDimension = math.max(size.width, size.height);
    
    if (maxDimension <= 360) return VideoResolution.p360;
    if (maxDimension <= 480) return VideoResolution.p480;
    if (maxDimension <= 720) return VideoResolution.p720;
    return VideoResolution.p1080;
  }
  
  static Future<File> _generateOutputFile(File originalFile, String suffix) async {
    final directory = await _getOutputDirectory();
    final fileName = '${const Uuid().v4()}_$suffix.mp4';
    return File('${directory.path}/$fileName');
  }
  
  static Future<Directory> _getOutputDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final outputDir = Directory('${appDir.path}/$_outputDir');
    
    if (!await outputDir.exists()) {
      await outputDir.create(recursive: true);
    }
    
    return outputDir;
  }
  
  /// Clean up output files
  static Future<void> clearOutputFiles() async {
    try {
      final outputDir = await _getOutputDirectory();
      if (await outputDir.exists()) {
        await outputDir.delete(recursive: true);
      }
    } catch (e) {
      print('Error clearing output files: $e');
    }
  }
  
  /// Get size of output files cache
  static Future<int> getOutputCacheSize() async {
    try {
      final outputDir = await _getOutputDirectory();
      if (!await outputDir.exists()) return 0;
      
      int totalSize = 0;
      await for (var entity in outputDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      print('Error calculating output cache size: $e');
      return 0;
    }
  }
}