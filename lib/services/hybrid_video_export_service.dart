import 'dart:io';
import 'dart:math' as math;
import '../screens/project/models/video_track_model.dart';
import '../screens/project/models/text_track_model.dart';
import '../screens/project/models/audio_track_model.dart';
import 'package:flutter/material.dart';

/// Enhanced video export service that works with preprocessed files
/// Uses your existing FFmpeg export pipeline but with lighter preprocessed files
class HybridVideoExportService {
  
  /// Generate FFmpeg command using preprocessed files + canvas transforms
  static String generateFFmpegCommand({
    required List<VideoTrackModel> videoTracks,
    required List<TextTrackModel> textTracks,
    required List<AudioTrackModel> audioTracks,
    required Size canvasSize,
    required String outputPath,
    String videoCodec = 'libx264',
    String audioCodec = 'aac',
    String preset = 'medium',
    int crf = 23,
    String audioBitrate = '128k',
  }) {
    final inputs = <String>[];
    final filters = <String>[];
    
    // Build input files (using preprocessed files where available)
    int inputIndex = 0;
    for (final track in videoTracks) {
      inputs.add('-i "${track.activeFile.path}"'); // Uses preprocessed file if available!
      inputIndex++;
    }
    
    // Add audio inputs
    for (final track in audioTracks) {
      inputs.add('-i "${track.filePath}"');
      inputIndex++;
    }
    
    // Build filter complex for canvas composition
    String filterComplex = _buildCanvasFilterComplex(
      videoTracks, 
      textTracks, 
      audioTracks,
      canvasSize
    );
    
    final command = [
      'ffmpeg',
      ...inputs,
      '-filter_complex', '"$filterComplex"',
      '-map', '[final_video]',
      if (audioTracks.isNotEmpty) ...['-map', '[final_audio]'],
      '-c:v', videoCodec,
      '-preset', preset,
      '-crf', crf.toString(),
      if (audioTracks.isNotEmpty) ...[
        '-c:a', audioCodec,
        '-b:a', audioBitrate,
      ],
      '-y', // Overwrite output
      '"$outputPath"'
    ].join(' ');
    
    return command;
  }
  
  /// Build filter complex for canvas composition with preprocessed files
  static String _buildCanvasFilterComplex(
    List<VideoTrackModel> videoTracks,
    List<TextTrackModel> textTracks,
    List<AudioTrackModel> audioTracks,
    Size canvasSize,
  ) {
    final filterSteps = <String>[];
    
    // Step 1: Process each video track (now using preprocessed files)
    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];
      final filters = <String>[];
      
      // Note: Destructive edits (crop, trim, speed, rotation) are already 
      // applied in the preprocessed file, so we only need canvas transforms
      
      // Canvas scale (non-destructive transform)
      if (track.canvasScale != 1.0) {
        final scaledWidth = (track.canvasSize.width * track.canvasScale).toInt();
        final scaledHeight = (track.canvasSize.height * track.canvasScale).toInt();
        filters.add('scale=$scaledWidth:$scaledHeight');
      }
      
      // Canvas rotation (if not already applied in preprocessing)
      if (track.canvasRotation != 0 && !_wasRotationPreprocessed(track)) {
        final radians = track.canvasRotation * math.pi / 180;
        filters.add('rotate=$radians:fillcolor=black@0');
      }
      
      // Canvas opacity
      if (track.canvasOpacity != 1.0) {
        filters.add('format=rgba,colorchannelmixer=aa=${track.canvasOpacity}');
      }
      
      // Apply filters to input
      if (filters.isNotEmpty) {
        filterSteps.add('[$i:v]${filters.join(',')}[v$i]');
      } else {
        filterSteps.add('[$i:v]copy[v$i]');
      }
    }
    
    // Step 2: Create canvas background
    filterSteps.add(
      'color=c=black:s=${canvasSize.width.toInt()}x${canvasSize.height.toInt()}:d=10[bg]'
    );
    
    // Step 3: Overlay videos on canvas (respecting z-index and visibility)
    final visibleTracks = videoTracks
        .where((track) => track.canvasVisible)
        .toList()
      ..sort((a, b) => a.canvasZIndex.compareTo(b.canvasZIndex));
    
    String currentLayer = '[bg]';
    
    for (int i = 0; i < visibleTracks.length; i++) {
      final track = visibleTracks[i];
      final trackIndex = videoTracks.indexOf(track);
      
      final x = track.canvasPosition.dx.toInt();
      final y = track.canvasPosition.dy.toInt();
      
      filterSteps.add(
        '$currentLayer[v$trackIndex]overlay=$x:$y[overlay$i]'
      );
      currentLayer = '[overlay$i]';
    }
    
    // Step 4: Add text overlays
    for (int i = 0; i < textTracks.length; i++) {
      final text = textTracks[i];
      if (text.isVisible) {
        final textFilter = _generateTextFilter(text);
        filterSteps.add('$currentLayer$textFilter[text$i]');
        currentLayer = '[text$i]';
      }
    }
    
    // Step 5: Process audio tracks
    if (audioTracks.isNotEmpty) {
      final audioInputs = <String>[];
      for (int i = 0; i < audioTracks.length; i++) {
        final audioIndex = videoTracks.length + i;
        audioInputs.add('[$audioIndex:a]');
      }
      
      if (audioInputs.length == 1) {
        filterSteps.add('${audioInputs.first}copy[final_audio]');
      } else {
        filterSteps.add('${audioInputs.join('')}amix=inputs=${audioInputs.length}[final_audio]');
      }
    }
    
    // Final step: Label video output
    filterSteps.add('${currentLayer}copy[final_video]');
    
    return filterSteps.join(';');
  }
  
  /// Check if rotation was applied during preprocessing
  static bool _wasRotationPreprocessed(VideoTrackModel track) {
    return track.appliedPreprocessing?.rotationDegrees != null && 
           track.appliedPreprocessing!.rotationDegrees != 0;
  }
  
  /// Generate text overlay filter
  static String _generateTextFilter(TextTrackModel text) {
    final escapedText = text.text.replaceAll("'", "\\'").replaceAll(":", "\\:");
    
    return 'drawtext=text=\'$escapedText\':'
           'x=${text.position.dx.toInt()}:'
           'y=${text.position.dy.toInt()}:'
           'fontsize=${text.fontSize.toInt()}:'
           'fontcolor=${_colorToHex(text.color)}:'
           'enable=\'between(t,${text.startTime},${text.endTime})\'';
  }
  
  /// Convert Flutter color to hex string for FFmpeg
  static String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }
  
  /// Get export summary showing preprocessing benefits
  static Map<String, dynamic> getExportSummary({
    required List<VideoTrackModel> videoTracks,
    required List<TextTrackModel> textTracks,
    required List<AudioTrackModel> audioTracks,
  }) {
    int preprocessedCount = 0;
    int totalFileSize = 0;
    int preprocessedFileSize = 0;
    List<String> appliedPreprocessing = [];
    
    for (final track in videoTracks) {
      try {
        totalFileSize += track.originalFile.lengthSync();
        
        if (track.isPreprocessed) {
          preprocessedCount++;
          preprocessedFileSize += track.preprocessedFile!.lengthSync();
          
          final options = track.appliedPreprocessing;
          if (options != null) {
            if (options.trimOptions != null) appliedPreprocessing.add('trimmed');
            if (options.cropOptions != null) appliedPreprocessing.add('cropped');
            if (options.rotationDegrees != 0) appliedPreprocessing.add('rotated');
            if (options.speedFactor != 1.0) appliedPreprocessing.add('speed adjusted');
            if (options.compressionOptions != null) appliedPreprocessing.add('compressed');
            if (options.audioOptions?.mute == true) appliedPreprocessing.add('muted');
          }
        } else {
          preprocessedFileSize += track.activeFile.lengthSync();
        }
      } catch (e) {
        // Handle file access errors
      }
    }
    
    final spaceSavings = totalFileSize - preprocessedFileSize;
    final spaceSavingsPercent = totalFileSize > 0 ? (spaceSavings / totalFileSize * 100) : 0;
    
    return {
      'totalVideoTracks': videoTracks.length,
      'preprocessedTracks': preprocessedCount,
      'totalTextTracks': textTracks.length,
      'totalAudioTracks': audioTracks.length,
      'originalFilesSize': totalFileSize,
      'processedFilesSize': preprocessedFileSize,
      'spaceSavings': spaceSavings,
      'spaceSavingsPercent': spaceSavingsPercent,
      'appliedPreprocessing': appliedPreprocessing.toSet().toList(),
      'processingStrategy': 'Hybrid (easy_video_editor + FFmpeg)',
      'benefits': _generateBenefitsList(preprocessedCount, spaceSavingsPercent, appliedPreprocessing),
    };
  }
  
  static List<String> _generateBenefitsList(
    int preprocessedCount, 
    double spaceSavingsPercent, 
    List<String> appliedPreprocessing
  ) {
    final benefits = <String>[];
    
    if (preprocessedCount > 0) {
      benefits.add('$preprocessedCount video${preprocessedCount > 1 ? 's' : ''} optimized with easy_video_editor');
    }
    
    if (spaceSavingsPercent > 10) {
      benefits.add('${spaceSavingsPercent.toStringAsFixed(1)}% file size reduction');
    }
    
    if (appliedPreprocessing.isNotEmpty) {
      benefits.add('Quality-preserved ${appliedPreprocessing.join(', ')} operations');
    }
    
    benefits.add('Faster export with lighter file processing');
    benefits.add('Multi-video canvas composition via FFmpeg');
    
    return benefits;
  }
  
  /// Generate export command with performance optimizations
  static String generateOptimizedFFmpegCommand({
    required List<VideoTrackModel> videoTracks,
    required List<TextTrackModel> textTracks,
    required List<AudioTrackModel> audioTracks,
    required Size canvasSize,
    required String outputPath,
    bool fastStart = true,
    bool useHardwareAcceleration = false,
  }) {
    var command = generateFFmpegCommand(
      videoTracks: videoTracks,
      textTracks: textTracks,
      audioTracks: audioTracks,
      canvasSize: canvasSize,
      outputPath: outputPath,
    );
    
    // Add performance optimizations
    final optimizations = <String>[];
    
    if (fastStart) {
      optimizations.add('-movflags +faststart');
    }
    
    if (useHardwareAcceleration) {
      // Add hardware acceleration if available (you may want to detect this)
      optimizations.add('-hwaccel auto');
    }
    
    // Insert optimizations before the output path
    if (optimizations.isNotEmpty) {
      command = command.replaceAll(
        '-y "$outputPath"', 
        '${optimizations.join(' ')} -y "$outputPath"'
      );
    }
    
    return command;
  }
  
  /// Validate export configuration
  static Map<String, dynamic> validateExportConfig({
    required List<VideoTrackModel> videoTracks,
    required List<TextTrackModel> textTracks,
    required List<AudioTrackModel> audioTracks,
    required Size canvasSize,
  }) {
    final issues = <String>[];
    final warnings = <String>[];
    
    // Check for empty project
    if (videoTracks.isEmpty && textTracks.isEmpty && audioTracks.isEmpty) {
      issues.add('Project is empty - no content to export');
    }
    
    // Check for preprocessing in progress
    for (final track in videoTracks) {
      if (track.isPreprocessing) {
        issues.add('Video preprocessing in progress for track ${track.id}');
      }
    }
    
    // Check for missing files
    for (final track in videoTracks) {
      if (!track.activeFile.existsSync()) {
        issues.add('Video file not found: ${track.activeFile.path}');
      }
    }
    
    for (final track in audioTracks) {
      if (!File(track.filePath).existsSync()) {
        issues.add('Audio file not found: ${track.filePath}');
      }
    }
    
    // Performance warnings
    final largeVideos = videoTracks.where((track) {
      try {
        return track.activeFile.lengthSync() > 500 * 1024 * 1024; // 500MB
      } catch (e) {
        return false;
      }
    }).length;
    
    if (largeVideos > 0) {
      warnings.add('$largeVideos large video file${largeVideos > 1 ? 's' : ''} detected - consider preprocessing for faster export');
    }
    
    if (videoTracks.length > 4) {
      warnings.add('${videoTracks.length} video tracks - export may take longer');
    }
    
    return {
      'isValid': issues.isEmpty,
      'issues': issues,
      'warnings': warnings,
      'canProceed': issues.isEmpty,
    };
  }
}