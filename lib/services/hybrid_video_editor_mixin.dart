import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../models/preprocessing_options.dart';
import '../screens/project/models/video_track_model.dart';
import '../controllers/video_controller.dart';
import 'video_preprocessing_service.dart';

/// Mixin to add hybrid preprocessing capabilities to VideoEditorProvider
/// Integrates easy_video_editor for single-track preprocessing while maintaining FFmpeg for multi-video
mixin HybridVideoEditorMixin on ChangeNotifier {
  
  // Abstract getters that must be implemented by the VideoEditorProvider
  List<VideoTrackModel> get videoTracks;
  void updateVideoTrack(int index, VideoTrackModel track);
  VideoEditorController? getVideoControllerForTrack(String trackId);
  Size get canvasSize; // Add this getter to your VideoEditorProvider if not exists

  /// Apply preprocessing to a video track (maintains UI continuity)
  Future<void> preprocessVideoTrack({
    required String trackId,
    required PreprocessingOptions options,
  }) async {
    final trackIndex = videoTracks.indexWhere((t) => t.id == trackId);
    if (trackIndex == -1) return;

    final track = videoTracks[trackIndex];
    
    // Start preprocessing indicator
    updateVideoTrack(trackIndex, track.copyWith(
      isPreprocessing: true,
      preprocessingProgress: 0.0,
    ));

    try {
      // Preprocess with easy_video_editor
      final preprocessedFile = await VideoPreprocessingService.preprocessVideo(
        originalFile: track.activeFile, // Use current active file
        options: options,
        onProgress: (progress) {
          final updatedTrack = videoTracks[trackIndex].copyWith(
            preprocessingProgress: progress,
          );
          updateVideoTrack(trackIndex, updatedTrack);
        },
      );

      // Create new video controller with preprocessed file
      final newController = await _createUpdatedVideoController(trackId, preprocessedFile);
      await updateVideoControllerForTrack(trackId, newController);

      // Update track model
      final finalTrack = track.copyWith(
        preprocessedFile: preprocessedFile,
        appliedPreprocessing: options,
        isPreprocessing: false,
        preprocessingProgress: 1.0,
        // Reset canvas crop if crop was applied in preprocessing
        canvasCropModel: options.cropOptions != null 
            ? CropModel(enabled: false) // Reset canvas crop
            : track.canvasCropModel,
      );
      
      updateVideoTrack(trackIndex, finalTrack);
      
    } catch (e) {
      // Handle preprocessing error
      final errorTrack = track.copyWith(
        isPreprocessing: false,
        preprocessingProgress: 0.0,
      );
      updateVideoTrack(trackIndex, errorTrack);
      rethrow;
    }
  }

  /// Replace video controller file while maintaining playback state
  /// Note: This requires updating the VideoEditorController or creating a new one
  Future<VideoEditorController> _createUpdatedVideoController(
    String trackId,
    File newFile
  ) async {
    // Create new controller with the preprocessed file
    final newController = VideoEditorController.file(newFile);
    await newController.initialize();
    
    return newController;
  }

  /// Abstract method to be implemented by VideoEditorProvider
  /// This should update the controller for the given track ID
  Future<void> updateVideoControllerForTrack(String trackId, VideoEditorController newController);

  /// Smart preprocessing: Apply crop through easy_video_editor instead of canvas
  Future<void> applyCropToTrack({
    required String trackId,
    required CropModel cropModel,
  }) async {
    if (!cropModel.enabled || !cropModel.isValid) return;

    final cropOptions = CropOptions.fromCropModel(cropModel);
    final preprocessingOptions = PreprocessingOptions(
      cropOptions: cropOptions,
    );

    await preprocessVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Smart preprocessing: Apply trim through easy_video_editor
  Future<void> applyTrimToTrack({
    required String trackId,
    required double startSeconds,
    required double endSeconds,
  }) async {
    final trimOptions = TrimOptions(
      startSeconds: startSeconds,
      endSeconds: endSeconds,
    );
    final preprocessingOptions = PreprocessingOptions(
      trimOptions: trimOptions,
    );

    await preprocessVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply rotation via preprocessing
  Future<void> applyRotationToTrack({
    required String trackId,
    required int degrees,
  }) async {
    final preprocessingOptions = PreprocessingOptions(
      rotationDegrees: degrees,
    );

    await preprocessVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply speed adjustment via preprocessing
  Future<void> applySpeedToTrack({
    required String trackId,
    required double speedFactor,
  }) async {
    final preprocessingOptions = PreprocessingOptions(
      speedFactor: speedFactor,
    );

    await preprocessVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply compression via preprocessing
  Future<void> applyCompressionToTrack({
    required String trackId,
    required VideoQuality quality,
    String? resolution,
  }) async {
    final compressionOptions = CompressionOptions(
      quality: quality,
      resolution: resolution,
    );
    final preprocessingOptions = PreprocessingOptions(
      compressionOptions: compressionOptions,
    );

    await preprocessVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Apply audio processing via preprocessing
  Future<void> applyAudioProcessingToTrack({
    required String trackId,
    bool mute = false,
    bool extractAudio = false,
  }) async {
    final audioOptions = AudioOptions(
      mute: mute,
      extractAudio: extractAudio,
    );
    final preprocessingOptions = PreprocessingOptions(
      audioOptions: audioOptions,
    );

    await preprocessVideoTrack(
      trackId: trackId,
      options: preprocessingOptions,
    );
  }

  /// Decide whether to use easy_video_editor or FFmpeg for specific operations
  ProcessingStrategy decideProcessingStrategy({
    required VideoTrackModel track,
    required EditType editType,
  }) {
    
    // Always use easy_video_editor for single destructive edits
    if (_isDestructiveEdit(editType)) {
      return ProcessingStrategy.easyVideoEditor;
    }
    
    // Use FFmpeg for multi-video operations
    if (videoTracks.length > 1 && _isCompositeEdit(editType)) {
      return ProcessingStrategy.ffmpeg;
    }
    
    // Use easy_video_editor for heavy single-video operations
    if (_isHeavyOperation(editType, track)) {
      return ProcessingStrategy.easyVideoEditor;
    }
    
    return ProcessingStrategy.ffmpeg; // Default to FFmpeg
  }
  
  bool _isDestructiveEdit(EditType editType) {
    return [
      EditType.crop,
      EditType.trim,
      EditType.speed,
      EditType.rotate,
      EditType.compress,
      EditType.audioExtract,
    ].contains(editType);
  }
  
  bool _isCompositeEdit(EditType editType) {
    return [
      EditType.overlay,
      EditType.blend,
      EditType.transition,
      EditType.textOverlay,
    ].contains(editType);
  }
  
  bool _isHeavyOperation(EditType editType, VideoTrackModel track) {
    // Large file size (>100MB)
    try {
      if (track.originalFile.lengthSync() > 100 * 1024 * 1024) {
        return true;
      }
    } catch (e) {
      // File access error, assume not heavy
    }
    
    // High resolution compared to canvas
    final controller = getVideoControllerForTrack(track.id);
    if (controller != null) {
      final videoSize = controller.video.value.size;
      if (videoSize.width > 0 && videoSize.height > 0) {
        final scaleFactor = math.max(
          videoSize.width / canvasSize.width,
          videoSize.height / canvasSize.height,
        );
        
        // Consider heavy if video is significantly larger than canvas
        if (scaleFactor > 2.0) {
          return true;
        }
      }
    }
    
    return false;
  }

  /// Generate optimized preprocessing options based on canvas and track properties
  PreprocessingOptions optimizePreprocessingOptions({
    required PreprocessingOptions original,
    required VideoTrackModel track,
  }) {
    // Smart compression: compress if source is much larger than canvas
    CompressionOptions? compression = original.compressionOptions;
    
    final controller = getVideoControllerForTrack(track.id);
    if (controller != null && compression == null) {
      final videoSize = controller.video.value.size;
      if (videoSize.width > 0 && videoSize.height > 0) {
        final scaleFactor = math.max(
          videoSize.width / canvasSize.width,
          videoSize.height / canvasSize.height,
        );
        
        // Compress if video is significantly larger than needed
        if (scaleFactor > 2.0) {
          compression = CompressionOptions(
            quality: VideoQuality.high, // Still high quality
            resolution: '${canvasSize.width.toInt()}x${canvasSize.height.toInt()}',
          );
        }
      }
    }
    
    return original.copyWith(compressionOptions: compression);
  }

  /// Get track by ID
  VideoTrackModel? getTrackById(String trackId) {
    try {
      return videoTracks.firstWhere((track) => track.id == trackId);
    } catch (e) {
      return null;
    }
  }

  /// Check if any tracks are currently preprocessing
  bool get hasPreprocessingTracks {
    return videoTracks.any((track) => track.isPreprocessing);
  }

  /// Get preprocessing progress for UI display
  double get overallPreprocessingProgress {
    final processingTracks = videoTracks.where((track) => track.isPreprocessing).toList();
    if (processingTracks.isEmpty) return 1.0;
    
    final totalProgress = processingTracks.fold<double>(
      0.0, 
      (sum, track) => sum + track.preprocessingProgress
    );
    
    return totalProgress / processingTracks.length;
  }

  /// Get preprocessing status message for UI
  String get preprocessingStatusMessage {
    final processingTracks = videoTracks.where((track) => track.isPreprocessing).toList();
    if (processingTracks.isEmpty) return '';
    
    if (processingTracks.length == 1) {
      return 'Processing video...';
    } else {
      return 'Processing ${processingTracks.length} videos...';
    }
  }

  /// Clear all preprocessing data (useful for cleanup)
  Future<void> clearPreprocessingData() async {
    // Clear service cache
    await VideoPreprocessingService.clearPreprocessedFiles();
    
    // Reset all tracks preprocessing state
    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];
      if (track.isPreprocessed || track.isPreprocessing) {
        updateVideoTrack(i, track.copyWith(
          preprocessedFile: null,
          appliedPreprocessing: null,
          isPreprocessing: false,
          preprocessingProgress: 0.0,
        ));
      }
    }
  }
}