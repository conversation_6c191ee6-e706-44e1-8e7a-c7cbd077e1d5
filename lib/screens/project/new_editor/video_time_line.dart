import 'dart:math' as math;
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_track.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_trimmer.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/stretch_duration_selector.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/enums/track_type.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:ai_video_creator_editor/components/track_options.dart';

class VideoTimeline extends StatefulWidget {
  final VideoPlayerController? controller;
  final ScrollController? videoScrollController;

  const VideoTimeline({
    super.key,
    required this.controller,
    required this.videoScrollController,
  });

  @override
  State<VideoTimeline> createState() => _VideoTimelineState();
}

class _VideoTimelineState extends State<VideoTimeline>
    with AutomaticKeepAliveClientMixin {
  @override
  void initState() {
    // Set up scroll listener for manual timeline scrolling
    widget.videoScrollController
        ?.removeListener(_onScrollToUpdateVideoPosition);
    widget.videoScrollController?.addListener(() {
      if (widget.videoScrollController?.position.isScrollingNotifier.value ??
          false) {
        _onScrollToUpdateVideoPosition();
      }
    });

    // Set up position update listener for playback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupPlaybackListener();
    });

    super.initState();
  }

  void _setupPlaybackListener() {
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);

    // Store the original callback to preserve provider's notifyListeners
    final originalCallback =
        provider.masterTimelineController.onPositionChanged;

    // Chain callbacks instead of overriding
    provider.masterTimelineController.onPositionChanged = () {
      // Call original callback first (triggers notifyListeners in provider)
      originalCallback?.call();

      // Then do our timeline-specific logic
      if (provider.masterTimelineController.isPlaying) {
        _onPlayScrollToCurrentVideoPosition();
      }
    };
  }

  Future<void> _onScrollToUpdateVideoPosition() async {
    if (!mounted || widget.videoScrollController?.hasClients != true) return;

    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    final scrollController = widget.videoScrollController!;

    // Update master timeline position based on scroll
    provider.masterTimelineController.seekFromScroll(
      scrollController.offset,
      scrollController.position.maxScrollExtent,
    );

    // Manually trigger provider update to ensure preview refreshes
    // This is needed because seekFromScroll uses debouncing
    provider.notifyListeners();
  }

  void _onPlayScrollToCurrentVideoPosition() {
    if (!mounted || widget.videoScrollController?.hasClients != true) return;

    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    final scrollController = widget.videoScrollController!;

    // Get target scroll position from master timeline
    final double targetOffset =
        provider.masterTimelineController.getScrollOffset(
      scrollController.position.maxScrollExtent,
    );

    // Only update if there's a significant difference to avoid jitter
    if ((targetOffset - scrollController.offset).abs() > 5) {
      scrollController.jumpTo(targetOffset);
    }
  }

  @override
  void dispose() {
    widget.videoScrollController
        ?.removeListener(_onScrollToUpdateVideoPosition);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final width = MediaQuery.of(context).size.width;
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        // FIXED: Calculate timeline width as sum of individual track widths
        // This ensures the timeline container exactly matches the actual content width
        double timelineWidth = 0.0;
        for (var track in provider.videoTracks) {
          timelineWidth += (width / 8) * track.totalDuration;
        }

        // Fallback for empty tracks
        if (provider.videoTracks.isEmpty) {
          timelineWidth = 100.0; // Minimum width for empty timeline
        }

        // Debug logging (can be removed later)
        final providerDuration = provider.videoDuration;
        final providerWidth = providerDuration * (width / 8);
        final oldMargin = width * 0.1; // Previous margin
        final newMargin = width / 2; // Timeline margin for scrollability
        print('🎯 TIMELINE WIDTH & MARGIN FIX:');
        print(
            '   Provider videoDuration: ${providerDuration}s (width: ${providerWidth.toStringAsFixed(1)}px)');
        print(
            '   Fixed timeline width: ${timelineWidth.toStringAsFixed(1)}px (sum of tracks)');
        print(
            '   Old margin: ${oldMargin.toStringAsFixed(1)}px (10% of screen)');
        print(
            '   New margin: ${newMargin.toStringAsFixed(1)}px (fixed padding)');
        print(
            '   Total space saved: ${(providerWidth - timelineWidth + oldMargin - newMargin).toStringAsFixed(1)}px');
        return Container(
          margin: EdgeInsets.only(
              right:
                  newMargin), // Minimal padding instead of 10% of screen width
          width: timelineWidth,
          child: Row(
            children: provider.videoTracks.asMap().entries.map((entry) {
              final index = entry.key;
              final videoTrack = entry.value;
              return VideoTrack(
                key: ValueKey(
                    '${videoTrack.id}_${videoTrack.lastModified.millisecondsSinceEpoch}'),
                videoTrack: videoTrack,
                index: index,
                isSelected: provider.selectedVideoTrackIndex == index,
                selectedTrackBorderColor: provider.selectedTrackBorderColor,
                onLongPress: (Offset globalPosition) => _showTrackOptions(
                    context, globalPosition, videoTrack, index, provider),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  void _openVideoTrimmer(
      BuildContext context, VideoTrackModel videoTrack, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoTrimmer(
          videoTrack: videoTrack,
          trackIndex: index,
        ),
      ),
    );
  }

  void _showStretchDuration(BuildContext context, VideoTrackModel videoTrack,
      int index, VideoEditorProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StretchDurationSelector(
        currentDuration: videoTrack.totalDuration,
        onDurationSelected: (newDuration) async {
          await provider.stretchImageVideo(index, newDuration);
        },
      ),
    );
  }

  void _showTrackOptions(
    BuildContext context,
    Offset globalPosition,
    VideoTrackModel videoTrack,
    int index,
    VideoEditorProvider provider,
  ) async {
    OverlayEntry? entry;

    // Don't show mute option for image-based videos
    final bool showMute =
        videoTrack.hasOriginalAudio && !videoTrack.isImageBased;
    final bool? isMuted =
        showMute ? provider.isVideoMuted(videoTrack.id) : null;

    entry = OverlayEntry(
      builder: (context) => TrackOptions(
        offset: globalPosition,
        trackType: TrackType.video,
        onTap: () {
          entry?.remove();
        },
        onTrim: () {
          entry?.remove();
          provider.setVideoTrackIndex(index);
        },
        onDelete: () async {
          context.loaderOverlay.show();
          try {
            await provider.removeVideoTrack(index);
          } finally {
            context.loaderOverlay.hide();
          }
          entry?.remove();
        },
        onMute: showMute
            ? () {
                provider.toggleVideoMute(videoTrack.id);
                entry?.remove();
              }
            : null,
        onStretch: videoTrack.isImageBased
            ? () {
                entry?.remove();
                _showStretchDuration(context, videoTrack, index, provider);
              }
            : null,
        showStretch: videoTrack.isImageBased,
        showMute: showMute,
        isMuted: isMuted,
      ),
    );
    Overlay.of(context, rootOverlay: true).insert(entry);
  }

  @override
  bool get wantKeepAlive => true;
}
