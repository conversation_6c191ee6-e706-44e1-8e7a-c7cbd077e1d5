import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'transition_picker.dart';

/// Widget that provides live transition preview effects between video clips
class TransitionPreviewWidget extends StatefulWidget {
  final VideoPlayerController? currentController;
  final VideoPlayerController? nextController;
  final TransitionType transitionType;
  final double transitionProgress; // 0.0 to 1.0
  final Duration transitionDuration;
  final Widget child;

  const TransitionPreviewWidget({
    Key? key,
    this.currentController,
    this.nextController,
    required this.transitionType,
    required this.transitionProgress,
    this.transitionDuration = const Duration(seconds: 1),
    required this.child,
  }) : super(key: key);

  @override
  State<TransitionPreviewWidget> createState() => _TransitionPreviewWidgetState();
}

class _TransitionPreviewWidgetState extends State<TransitionPreviewWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.transitionDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void didUpdateWidget(TransitionPreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.transitionProgress != widget.transitionProgress) {
      _animationController.animateTo(widget.transitionProgress);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.transitionType == TransitionType.none || 
        widget.currentController == null) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _buildTransitionEffect();
      },
    );
  }

  Widget _buildTransitionEffect() {
    final progress = _animation.value;
    
    switch (widget.transitionType) {
      case TransitionType.fade:
        return _buildFadeTransition(progress);
      
      case TransitionType.fadeblack:
        return _buildFadeBlackTransition(progress);
      
      case TransitionType.fadewhite:
        return _buildFadeWhiteTransition(progress);
      
      case TransitionType.wipeleft:
        return _buildWipeLeftTransition(progress);
      
      case TransitionType.wiperight:
        return _buildWipeRightTransition(progress);
      
      case TransitionType.slideleft:
        return _buildSlideLeftTransition(progress);
      
      case TransitionType.slideright:
        return _buildSlideRightTransition(progress);
      
      case TransitionType.zoomin:
        return _buildZoomInTransition(progress);
      
      default:
        return _buildFadeTransition(progress);
    }
  }

  Widget _buildFadeTransition(double progress) {
    return Stack(
      children: [
        // Current video fading out
        Opacity(
          opacity: 1.0 - progress,
          child: widget.child,
        ),
        // Next video fading in (if available)
        if (widget.nextController != null)
          Opacity(
            opacity: progress,
            child: AspectRatio(
              aspectRatio: widget.nextController!.value.aspectRatio,
              child: VideoPlayer(widget.nextController!),
            ),
          ),
      ],
    );
  }

  Widget _buildFadeBlackTransition(double progress) {
    return Stack(
      children: [
        widget.child,
        // Black overlay
        Container(
          color: Colors.black.withOpacity(progress < 0.5 
            ? progress * 2 
            : (1.0 - progress) * 2),
        ),
        // Next video (if available and in second half)
        if (widget.nextController != null && progress > 0.5)
          Opacity(
            opacity: (progress - 0.5) * 2,
            child: AspectRatio(
              aspectRatio: widget.nextController!.value.aspectRatio,
              child: VideoPlayer(widget.nextController!),
            ),
          ),
      ],
    );
  }

  Widget _buildFadeWhiteTransition(double progress) {
    return Stack(
      children: [
        widget.child,
        // White overlay
        Container(
          color: Colors.white.withOpacity(progress < 0.5 
            ? progress * 2 
            : (1.0 - progress) * 2),
        ),
        // Next video (if available and in second half)
        if (widget.nextController != null && progress > 0.5)
          Opacity(
            opacity: (progress - 0.5) * 2,
            child: AspectRatio(
              aspectRatio: widget.nextController!.value.aspectRatio,
              child: VideoPlayer(widget.nextController!),
            ),
          ),
      ],
    );
  }

  Widget _buildWipeLeftTransition(double progress) {
    return ClipRect(
      child: Stack(
        children: [
          widget.child,
          // Next video wiping from left
          if (widget.nextController != null)
            Positioned(
              left: -MediaQuery.of(context).size.width * (1.0 - progress),
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: AspectRatio(
                  aspectRatio: widget.nextController!.value.aspectRatio,
                  child: VideoPlayer(widget.nextController!),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildWipeRightTransition(double progress) {
    return ClipRect(
      child: Stack(
        children: [
          widget.child,
          // Next video wiping from right
          if (widget.nextController != null)
            Positioned(
              right: -MediaQuery.of(context).size.width * (1.0 - progress),
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: AspectRatio(
                  aspectRatio: widget.nextController!.value.aspectRatio,
                  child: VideoPlayer(widget.nextController!),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSlideLeftTransition(double progress) {
    return Stack(
      children: [
        // Current video sliding out to left
        Transform.translate(
          offset: Offset(-MediaQuery.of(context).size.width * progress, 0),
          child: widget.child,
        ),
        // Next video sliding in from right
        if (widget.nextController != null)
          Transform.translate(
            offset: Offset(
              MediaQuery.of(context).size.width * (1.0 - progress), 
              0
            ),
            child: AspectRatio(
              aspectRatio: widget.nextController!.value.aspectRatio,
              child: VideoPlayer(widget.nextController!),
            ),
          ),
      ],
    );
  }

  Widget _buildSlideRightTransition(double progress) {
    return Stack(
      children: [
        // Current video sliding out to right
        Transform.translate(
          offset: Offset(MediaQuery.of(context).size.width * progress, 0),
          child: widget.child,
        ),
        // Next video sliding in from left
        if (widget.nextController != null)
          Transform.translate(
            offset: Offset(
              -MediaQuery.of(context).size.width * (1.0 - progress), 
              0
            ),
            child: AspectRatio(
              aspectRatio: widget.nextController!.value.aspectRatio,
              child: VideoPlayer(widget.nextController!),
            ),
          ),
      ],
    );
  }

  Widget _buildZoomInTransition(double progress) {
    return Stack(
      children: [
        // Current video zooming out
        Transform.scale(
          scale: 1.0 + progress * 0.5,
          child: Opacity(
            opacity: 1.0 - progress,
            child: widget.child,
          ),
        ),
        // Next video zooming in
        if (widget.nextController != null)
          Transform.scale(
            scale: 0.5 + progress * 0.5,
            child: Opacity(
              opacity: progress,
              child: AspectRatio(
                aspectRatio: widget.nextController!.value.aspectRatio,
                child: VideoPlayer(widget.nextController!),
              ),
            ),
          ),
      ],
    );
  }
}

/// Helper class to calculate transition progress based on timeline position
class TransitionProgressCalculator {
  static double calculateProgress({
    required double currentTime,
    required double transitionStartTime,
    required double transitionDuration,
  }) {
    if (currentTime < transitionStartTime) return 0.0;
    if (currentTime > transitionStartTime + transitionDuration) return 1.0;
    
    return (currentTime - transitionStartTime) / transitionDuration;
  }
  
  static bool isInTransition({
    required double currentTime,
    required double transitionStartTime,
    required double transitionDuration,
  }) {
    return currentTime >= transitionStartTime && 
           currentTime <= transitionStartTime + transitionDuration;
  }
}
