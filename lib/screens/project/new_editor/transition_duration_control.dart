import 'package:flutter/material.dart';
import 'transition_picker.dart';

/// Widget for controlling transition duration with visual feedback
class TransitionDurationControl extends StatefulWidget {
  final double initialDuration;
  final double minDuration;
  final double maxDuration;
  final TransitionType transitionType;
  final Function(double) onDurationChanged;
  final VoidCallback? onClose;

  const TransitionDurationControl({
    Key? key,
    required this.initialDuration,
    this.minDuration = 0.1,
    this.maxDuration = 5.0,
    required this.transitionType,
    required this.onDurationChanged,
    this.onClose,
  }) : super(key: key);

  @override
  State<TransitionDurationControl> createState() => _TransitionDurationControlState();
}

class _TransitionDurationControlState extends State<TransitionDurationControl>
    with TickerProviderStateMixin {
  late double _currentDuration;
  late AnimationController _previewController;
  late Animation<double> _previewAnimation;
  bool _isPreviewPlaying = false;

  @override
  void initState() {
    super.initState();
    _currentDuration = widget.initialDuration;
    
    _previewController = AnimationController(
      duration: Duration(milliseconds: (_currentDuration * 1000).round()),
      vsync: this,
    );
    
    _previewAnimation = CurvedAnimation(
      parent: _previewController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _previewController.dispose();
    super.dispose();
  }

  void _updateDuration(double newDuration) {
    setState(() {
      _currentDuration = newDuration;
    });
    
    _previewController.duration = Duration(milliseconds: (newDuration * 1000).round());
    widget.onDurationChanged(newDuration);
  }

  void _playPreview() {
    if (_isPreviewPlaying) {
      _previewController.stop();
      setState(() {
        _isPreviewPlaying = false;
      });
    } else {
      _previewController.forward(from: 0.0).then((_) {
        setState(() {
          _isPreviewPlaying = false;
        });
      });
      setState(() {
        _isPreviewPlaying = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Transition Duration',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.onClose != null)
                IconButton(
                  onPressed: widget.onClose,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Transition type info
          Row(
            children: [
              Icon(
                _getTransitionIcon(),
                color: Colors.white70,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                widget.transitionType.name.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Duration slider
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Duration: ${_currentDuration.toStringAsFixed(1)}s',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.blue,
                  inactiveTrackColor: Colors.grey[700],
                  thumbColor: Colors.blue,
                  overlayColor: Colors.blue.withOpacity(0.2),
                  trackHeight: 4,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: _currentDuration,
                  min: widget.minDuration,
                  max: widget.maxDuration,
                  divisions: ((widget.maxDuration - widget.minDuration) * 10).round(),
                  onChanged: _updateDuration,
                ),
              ),
              
              // Duration range labels
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${widget.minDuration}s',
                    style: const TextStyle(color: Colors.white54, fontSize: 12),
                  ),
                  Text(
                    '${widget.maxDuration}s',
                    style: const TextStyle(color: Colors.white54, fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Preview section
          Container(
            width: double.infinity,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.white24),
            ),
            child: AnimatedBuilder(
              animation: _previewAnimation,
              builder: (context, child) {
                return _buildTransitionPreview(_previewAnimation.value);
              },
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Preview controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _playPreview,
                icon: Icon(_isPreviewPlaying ? Icons.stop : Icons.play_arrow),
                label: Text(_isPreviewPlaying ? 'Stop Preview' : 'Play Preview'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Quick duration presets
          Wrap(
            spacing: 8,
            children: [0.5, 1.0, 1.5, 2.0, 3.0].map((duration) {
              final isSelected = (_currentDuration - duration).abs() < 0.1;
              return FilterChip(
                label: Text('${duration}s'),
                selected: isSelected,
                onSelected: (_) => _updateDuration(duration),
                selectedColor: Colors.blue.withOpacity(0.3),
                checkmarkColor: Colors.blue,
                labelStyle: TextStyle(
                  color: isSelected ? Colors.blue : Colors.white70,
                ),
                backgroundColor: Colors.grey[800],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  IconData _getTransitionIcon() {
    switch (widget.transitionType) {
      case TransitionType.fade:
      case TransitionType.fadeblack:
      case TransitionType.fadewhite:
        return Icons.blur_on;
      case TransitionType.wipeleft:
      case TransitionType.wiperight:
        return Icons.swipe_left;
      case TransitionType.slideleft:
      case TransitionType.slideright:
        return Icons.swap_horiz;
      case TransitionType.zoomin:
        return Icons.zoom_in;
      default:
        return Icons.animation;
    }
  }

  Widget _buildTransitionPreview(double progress) {
    return Stack(
      children: [
        // Background pattern
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue[800]!, Colors.blue[600]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Center(
            child: Text(
              'Video A',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        
        // Transition effect overlay
        _buildPreviewTransitionEffect(progress),
        
        // Progress indicator
        Positioned(
          bottom: 4,
          left: 4,
          right: 4,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white24,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewTransitionEffect(double progress) {
    switch (widget.transitionType) {
      case TransitionType.fade:
        return Container(
          color: Colors.green[800]!.withOpacity(progress),
          child: Center(
            child: Opacity(
              opacity: progress,
              child: const Text(
                'Video B',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        );
        
      case TransitionType.wipeleft:
        return ClipRect(
          child: Align(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              color: Colors.green[800],
              child: const Center(
                child: Text(
                  'Video B',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        );
        
      case TransitionType.slideleft:
        return Transform.translate(
          offset: Offset(-200 * (1 - progress), 0),
          child: Container(
            color: Colors.green[800],
            child: const Center(
              child: Text(
                'Video B',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        );
        
      default:
        return Container(
          color: Colors.green[800]!.withOpacity(progress),
          child: Center(
            child: Opacity(
              opacity: progress,
              child: const Text(
                'Video B',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        );
    }
  }
}
