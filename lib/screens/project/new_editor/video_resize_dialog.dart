import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';

class VideoResizeDialog extends StatefulWidget {
  final VideoTrackModel track;
  final Function(double) onResizeChanged;

  const VideoResizeDialog({
    Key? key,
    required this.track,
    required this.onResizeChanged,
  }) : super(key: key);

  @override
  State<VideoResizeDialog> createState() => _VideoResizeDialogState();
}

class _VideoResizeDialogState extends State<VideoResizeDialog> {
  late double _currentScale;
  late double _originalScale;

  @override
  void initState() {
    super.initState();
    _currentScale = widget.track.canvasScale;
    _originalScale = widget.track.canvasScale;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.grey[900],
      child: Container(
        width: 350,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Resize Video',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Scale preview
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    'Scale: ${(_currentScale * 100).toStringAsFixed(0)}%',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Original size: ${widget.track.canvasSize.width.toStringAsFixed(0)} x ${widget.track.canvasSize.height.toStringAsFixed(0)}',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                  Text(
                    'Scaled size: ${(widget.track.canvasSize.width * _currentScale).toStringAsFixed(0)} x ${(widget.track.canvasSize.height * _currentScale).toStringAsFixed(0)}',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Scale slider
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Scale',
                  style: TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Slider(
                  value: _currentScale,
                  min: 0.1,
                  max: 3.0,
                  divisions: 29,
                  activeColor: Colors.blue,
                  inactiveColor: Colors.grey,
                  onChanged: (value) {
                    setState(() {
                      _currentScale = value;
                    });
                    widget.onResizeChanged(value);
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('10%',
                        style: TextStyle(color: Colors.white70, fontSize: 12)),
                    Text('300%',
                        style: TextStyle(color: Colors.white70, fontSize: 12)),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Preset buttons
            Text(
              'Quick Presets',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildPresetButton('25%', 0.25),
                _buildPresetButton('50%', 0.5),
                _buildPresetButton('75%', 0.75),
                _buildPresetButton('100%', 1.0),
                _buildPresetButton('125%', 1.25),
                _buildPresetButton('150%', 1.5),
                _buildPresetButton('200%', 2.0),
              ],
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _currentScale = _originalScale;
                    });
                    widget.onResizeChanged(_originalScale);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[700],
                  ),
                  child: Text(
                    'Reset',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: Text(
                    'Done',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetButton(String label, double scale) {
    final isSelected = (_currentScale - scale).abs() < 0.01;
    return InkWell(
      onTap: () {
        setState(() {
          _currentScale = scale;
        });
        widget.onResizeChanged(scale);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey[700],
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[600]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
