import 'package:flutter/material.dart';
import '../models/text_track_model.dart';
import 'video_editor_page_updated.dart';
import 'package:provider/provider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/utils/text_auto_wrap_helper.dart';

class TextStyleEditor extends StatefulWidget {
  final TextTrackModel textTrack;
  final Function(TextTrackModel) onStyleUpdated;
  final Size? previewSize;

  const TextStyleEditor({
    super.key,
    required this.textTrack,
    required this.onStyleUpdated,
    this.previewSize,
  });

  @override
  TextStyleEditorState createState() => TextStyleEditorState();
}

class TextStyleEditorState extends State<TextStyleEditor> {
  late Color _selectedColor;
  late double _fontSize;
  late String _fontFamily;
  late double _positionX;
  late double _positionY;
  late double _rotation;

  final List<String> _fontFamilies = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Courier New',
    'Verdana',
    'Georgia',
    'Comic Sans MS',
    'Impact',
  ];

  @override
  void initState() {
    super.initState();
    _selectedColor = widget.textTrack.textColor;
    _fontSize = widget.textTrack.fontSize;
    _fontFamily = widget.textTrack.fontFamily;

    // Initialize position values - will be properly constrained in build method
    _positionX = widget.textTrack.position.dx;
    _positionY = widget.textTrack.position.dy;

    // Normalize rotation to 0-360 range to prevent slider errors
    _rotation = _normalizeRotation(widget.textTrack.rotation);
  }

  /// Normalize rotation values to 0-360 degree range
  double _normalizeRotation(double rotation) {
    // Handle negative values by adding 360 until positive
    while (rotation < 0) {
      rotation += 360;
    }

    // Handle values > 360 by taking modulo
    rotation = rotation % 360;

    // Ensure we don't have -0.0 (which can happen with floating point math)
    if (rotation == 0 && rotation.isNegative) {
      rotation = 0;
    }

    return rotation;
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<VideoEditorProvider>(context, listen: true);
    
    // Calculate dynamic canvas size with proper gap calculations like the preview system
    final containerSize = widget.previewSize ?? provider.canvasSize;
    final fittedCanvasSize = provider.selectedCanvasRatio.getOptimalCanvasSize(containerSize);
    
    // Calculate gaps using same logic as calculateOptimalCanvasSize()
    final containerAspect = containerSize.width / containerSize.height;
    final canvasAspect = provider.selectedCanvasRatio.aspectRatio;
    
    double gapLeft = 0.0, gapTop = 0.0;
    if (canvasAspect > containerAspect) {
      // Canvas is wider than container - letterbox top/bottom
      gapTop = (containerSize.height - fittedCanvasSize.height) / 2.0;
    } else {
      // Canvas is taller than container - letterbox left/right
      gapLeft = (containerSize.width - fittedCanvasSize.width) / 2.0;
    }
    
    // Calculate boundaries with gaps (like drag system)
    final double minX = gapLeft;
    final double minY = gapTop;
    final double maxX = gapLeft + fittedCanvasSize.width;
    final double maxY = gapTop + fittedCanvasSize.height;

    // Calculate text height for Y boundary using same logic as video editor
    final textStyle = TextStyle(
      color: _selectedColor,
      fontSize: _fontSize,
      fontFamily: _fontFamily,
    );

    // Use the same boundary calculation as video editor
    final boundaryBuffer = 10.0; // Buffer to prevent text from touching boundary
    final boundaryBufferY = 5.0;
    final availableWidth = maxX - _positionX - boundaryBuffer;
    final availableHeight = maxY - _positionY - boundaryBufferY;

    final wrappedLines = TextAutoWrapHelper.wrapTextToFit(
      widget.textTrack.text,
      availableWidth,
      availableHeight,
      textStyle,
    );
    final totalTextHeight = TextAutoWrapHelper.calculateWrappedTextHeight(
      wrappedLines,
      textStyle,
    );
    final maxValidY = maxY - totalTextHeight - boundaryBufferY;
    final clampedMaxY = maxValidY > minY ? maxValidY : minY;

    print('[TextStyleEditor] Canvas Position Debug:');
    print('  Container size: ${containerSize.width.round()} x ${containerSize.height.round()}');
    print('  Fitted canvas size: ${fittedCanvasSize.width.round()} x ${fittedCanvasSize.height.round()}');
    print('  Gaps: left=${gapLeft.round()}, top=${gapTop.round()}');
    print('  Original position: (${widget.textTrack.position.dx}, ${widget.textTrack.position.dy})');
    print('  Canvas bounds: X(${minX.round()} to ${maxX.round()}), Y(${minY.round()} to ${clampedMaxY.round()})');
    print(
        '  Constrained position: (${_positionX.round()}, ${_positionY.round()})');

    // Constrain position values to the calculated bounds (same as video editor)
    _positionX = _positionX.clamp(minX, maxX);
    _positionY = _positionY.clamp(minY, clampedMaxY);

    return BottomSheetWrapper(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Edit Text Style',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              SizedBox(height: 16),

              // Text Preview
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _selectedColor == Colors.black
                      ? Colors.white
                      : Colors.black,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey),
                ),
                child: Center(
                  child: Text(
                    widget.textTrack.text,
                    style: TextStyle(
                      color: _selectedColor,
                      fontSize: _fontSize,
                      fontFamily: _fontFamily,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 20),

              // Font Size Slider
              Text('Font Size: ${_fontSize.round()}px'),
              Slider(
                value: _fontSize,
                min: 12,
                max: 72,
                divisions: 60,
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                    // Recalculate boundaries when font size changes (same as video editor)
                    _recalculateBoundaries();
                  });
                },
              ),
              SizedBox(height: 16),

              // Font Family Dropdown
              Text('Font Family'),
              SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _fontFamily,
                decoration: InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: _fontFamilies.map((font) {
                  return DropdownMenuItem(
                    value: font,
                    child: Text(font, style: TextStyle(fontFamily: font)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _fontFamily = value;
                      // Recalculate boundaries when font family changes (same as video editor)
                      _recalculateBoundaries();
                    });
                  }
                },
              ),
              SizedBox(height: 16),

              // Color Picker
              Text('Text Color'),
              SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // Add black and white options first
                    GestureDetector(
                      onTap: () =>
                          setState(() => _selectedColor = Colors.black),
                      child: Container(
                        width: 40,
                        height: 40,
                        margin: EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _selectedColor == Colors.black
                                ? Colors.white
                                : Colors.transparent,
                            width: 3,
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () =>
                          setState(() => _selectedColor = Colors.white),
                      child: Container(
                        width: 40,
                        height: 40,
                        margin: EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _selectedColor == Colors.white
                                ? Colors.black
                                : Colors.grey,
                            width: 3,
                          ),
                        ),
                      ),
                    ),
                    // Add existing primary colors
                    ...Colors.primaries.map((color) {
                      final isSelected = _selectedColor == color;
                      return GestureDetector(
                        onTap: () => setState(() => _selectedColor = color),
                        child: Container(
                          width: 40,
                          height: 40,
                          margin: EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected
                                  ? Colors.white
                                  : Colors.transparent,
                              width: 3,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
              SizedBox(height: 16),

              // Rotation Slider
              Text('Rotation: ${_rotation.round()}°'),
              Slider(
                value: _rotation,
                min: 0,
                max: 360,
                divisions: 72,
                onChanged: (value) {
                  setState(() {
                    // Ensure rotation stays within valid range
                    _rotation = _normalizeRotation(value);
                  });
                },
              ),
              SizedBox(height: 16),

              // Position Controls
              Text('Position'),
              SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'X: ${_positionX.round()}px (${minX.round()} - ${maxX.round()})'),
                        Slider(
                          value: _positionX,
                          min: minX,
                          max: maxX,
                          onChanged: (value) {
                            setState(() {
                              _positionX = value;
                              // Recalculate Y boundaries when X changes (same as video editor drag)
                              final newAvailableWidth =
                                  maxX - _positionX - boundaryBuffer;
                              final newWrappedLines =
                                  TextAutoWrapHelper.wrapTextToFit(
                                widget.textTrack.text,
                                newAvailableWidth,
                                availableHeight,
                                textStyle,
                              );
                              final newTotalTextHeight =
                                  TextAutoWrapHelper.calculateWrappedTextHeight(
                                newWrappedLines,
                                textStyle,
                              );
                              final newMaxValidY =
                                  maxY - newTotalTextHeight - boundaryBufferY;
                              final newClampedMaxY =
                                  newMaxValidY > minY ? newMaxValidY : minY;

                              // Update Y position if it exceeds the new boundary
                              if (_positionY > newClampedMaxY) {
                                _positionY = newClampedMaxY;
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'Y: ${_positionY.round()}px (${minY.round()} - ${clampedMaxY.round()})'),
                        Slider(
                          value: _positionY,
                          min: minY,
                          max: clampedMaxY,
                          onChanged: (value) =>
                              setState(() => _positionY = value),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('Cancel'),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveChanges,
                      child: Text('Save Changes'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _recalculateBoundaries() {
    // This method will be called when font properties change
    // The actual recalculation happens in the build method
    // This is just a placeholder to trigger setState
  }

  void _saveChanges() {
    // Ensure rotation is normalized before saving
    final normalizedRotation = _normalizeRotation(_rotation);

    final updatedTrack = widget.textTrack.copyWith(
      textColor: _selectedColor,
      fontSize: _fontSize,
      fontFamily: _fontFamily,
      position: Offset(_positionX, _positionY),
      rotation: normalizedRotation,
    );

    widget.onStyleUpdated(updatedTrack);
    Navigator.pop(context);
  }
}
