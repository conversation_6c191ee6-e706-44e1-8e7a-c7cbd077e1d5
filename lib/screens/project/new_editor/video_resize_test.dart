import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:provider/provider.dart';

/// Test widget to verify video resize functionality
class VideoResizeTest extends StatefulWidget {
  const VideoResizeTest({Key? key}) : super(key: key);

  @override
  State<VideoResizeTest> createState() => _VideoResizeTestState();
}

class _VideoResizeTestState extends State<VideoResizeTest> {
  double _testScale = 1.0;

  @override
  Widget build(BuildContext context) {
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        if (provider.videoTracks.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'No video tracks available for resize test',
              style: TextStyle(color: Colors.white),
            ),
          );
        }

        final firstTrack = provider.videoTracks.first;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Video Resize Test',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Current track info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Track: ${firstTrack.id}',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                    Text(
                      'Current Scale: ${firstTrack.canvasScale.toStringAsFixed(2)}x',
                      style: TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Canvas Size: ${firstTrack.canvasSize.width.toStringAsFixed(0)} x ${firstTrack.canvasSize.height.toStringAsFixed(0)}',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                    Text(
                      'Scaled Size: ${(firstTrack.canvasSize.width * firstTrack.canvasScale).toStringAsFixed(0)} x ${(firstTrack.canvasSize.height * firstTrack.canvasScale).toStringAsFixed(0)}',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Test controls
              Text(
                'Test Scale: ${_testScale.toStringAsFixed(2)}x',
                style: TextStyle(color: Colors.white),
              ),
              Slider(
                value: _testScale,
                min: 0.1,
                max: 3.0,
                divisions: 29,
                activeColor: Colors.blue,
                onChanged: (value) {
                  setState(() {
                    _testScale = value;
                  });
                },
              ),

              const SizedBox(height: 16),

              // Action buttons
              Row(
                children: [
                  ElevatedButton(
                    onPressed: () {
                      print(
                          '🧪 TEST: Applying scale ${_testScale.toStringAsFixed(2)} to track ${firstTrack.id}');
                      provider.updateVideoTrackScale(firstTrack.id, _testScale);

                      // Verify the scale was applied
                      Future.delayed(Duration(milliseconds: 100), () {
                        final updatedTrack = provider.videoTracks
                            .firstWhere((t) => t.id == firstTrack.id);
                        print(
                            '🧪 TEST: Verified track scale after update: ${updatedTrack.canvasScale}');
                      });

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              'Applied scale: ${_testScale.toStringAsFixed(2)}x'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                    style:
                        ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                    child: Text('Apply Scale',
                        style: TextStyle(color: Colors.white)),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _testScale = 1.0;
                      });
                      provider.updateVideoTrackScale(firstTrack.id, 1.0);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Reset to original size'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[700]),
                    child: Text('Reset', style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Pinch-to-zoom instructions
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[900]?.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.green[700]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pinch-to-Zoom Instructions',
                      style: TextStyle(
                        color: Colors.green[300],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '1. Tap on video to select it',
                      style: TextStyle(color: Colors.green[200], fontSize: 12),
                    ),
                    Text(
                      '2. Use pinch gesture to zoom in/out',
                      style: TextStyle(color: Colors.green[200], fontSize: 12),
                    ),
                    Text(
                      '3. Drag to reposition video',
                      style: TextStyle(color: Colors.green[200], fontSize: 12),
                    ),
                    Text(
                      '4. Scale range: 10% - 500%',
                      style: TextStyle(color: Colors.green[200], fontSize: 12),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Export verification info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[900]?.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.blue[700]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Export Verification',
                      style: TextStyle(
                        color: Colors.blue[300],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '• Scale is applied during export via CanvasTransform',
                      style: TextStyle(color: Colors.blue[200], fontSize: 12),
                    ),
                    Text(
                      '• FFmpeg applies scale after positioning',
                      style: TextStyle(color: Colors.blue[200], fontSize: 12),
                    ),
                    Text(
                      '• Canvas dimensions remain consistent',
                      style: TextStyle(color: Colors.blue[200], fontSize: 12),
                    ),
                    Text(
                      '• Pinch-to-zoom changes are preserved in export',
                      style: TextStyle(color: Colors.blue[200], fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Helper function to show resize test in a dialog
void showVideoResizeTest(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => Dialog(
      backgroundColor: Colors.transparent,
      child: VideoResizeTest(),
    ),
  );
}
