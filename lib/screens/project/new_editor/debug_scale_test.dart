import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:provider/provider.dart';

/// Debug widget to test and verify scale functionality
class DebugScaleTest extends StatelessWidget {
  const DebugScaleTest({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red[900]?.withOpacity(0.8),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[700]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'DEBUG: Scale Test',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              
              // Show all video tracks and their scales
              if (provider.videoTracks.isEmpty)
                Text(
                  'No video tracks found',
                  style: TextStyle(color: Colors.white70),
                )
              else
                ...provider.videoTracks.map((track) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red[800]?.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Track: ${track.id.substring(0, 8)}...',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        'Scale: ${track.canvasScale.toStringAsFixed(3)}x',
                        style: TextStyle(color: Colors.white, fontSize: 14),
                      ),
                      Text(
                        'Position: (${track.canvasPosition.dx.toStringAsFixed(1)}, ${track.canvasPosition.dy.toStringAsFixed(1)})',
                        style: TextStyle(color: Colors.white70, fontSize: 10),
                      ),
                      Text(
                        'Size: ${track.canvasSize.width.toStringAsFixed(1)} x ${track.canvasSize.height.toStringAsFixed(1)}',
                        style: TextStyle(color: Colors.white70, fontSize: 10),
                      ),
                    ],
                  ),
                )).toList(),
              
              const SizedBox(height: 12),
              
              // Test buttons
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: provider.videoTracks.isEmpty ? null : () {
                      final firstTrack = provider.videoTracks.first;
                      print('🧪 DEBUG: Setting scale to 2.0x for track ${firstTrack.id}');
                      provider.updateVideoTrackScale(firstTrack.id, 2.0);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                    child: Text(
                      'Set 2.0x',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: provider.videoTracks.isEmpty ? null : () {
                      final firstTrack = provider.videoTracks.first;
                      print('🧪 DEBUG: Setting scale to 0.5x for track ${firstTrack.id}');
                      provider.updateVideoTrackScale(firstTrack.id, 0.5);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                    child: Text(
                      'Set 0.5x',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: provider.videoTracks.isEmpty ? null : () {
                      final firstTrack = provider.videoTracks.first;
                      print('🧪 DEBUG: Resetting scale to 1.0x for track ${firstTrack.id}');
                      provider.updateVideoTrackScale(firstTrack.id, 1.0);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[600],
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                    child: Text(
                      'Reset',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Export test button
              ElevatedButton(
                onPressed: provider.videoTracks.isEmpty ? null : () {
                  print('🧪 DEBUG: Current video tracks for export:');
                  for (int i = 0; i < provider.videoTracks.length; i++) {
                    final track = provider.videoTracks[i];
                    print('   Track $i: scale=${track.canvasScale}, id=${track.id}');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[700],
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                child: Text(
                  'Log Export Data',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Helper function to show debug scale test
void showDebugScaleTest(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => Dialog(
      backgroundColor: Colors.transparent,
      child: DebugScaleTest(),
    ),
  );
}
