import 'package:flutter/material.dart';
import 'transition_picker.dart';

/// Widget that displays transition indicators between video tracks in the timeline
class TransitionTimelineIndicator extends StatelessWidget {
  final TransitionType transitionType;
  final double transitionDuration;
  final double trackHeight;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;

  const TransitionTimelineIndicator({
    Key? key,
    required this.transitionType,
    required this.transitionDuration,
    this.trackHeight = 60.0,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (transitionType == TransitionType.none) {
      return const SizedBox.shrink();
    }

    final width = MediaQuery.of(context).size.width;
    final transitionWidth = (width / 8) * transitionDuration;

    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        width: transitionWidth,
        height: trackHeight,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _getTransitionColors(),
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          border: Border.all(
            color: isSelected ? Colors.white : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Stack(
          children: [
            // Transition pattern overlay
            _buildTransitionPattern(),
            
            // Transition icon and label
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getTransitionIcon(),
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _getTransitionLabel(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            // Duration indicator
            Positioned(
              bottom: 2,
              right: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${transitionDuration.toStringAsFixed(1)}s',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getTransitionColors() {
    switch (transitionType) {
      case TransitionType.fade:
        return [Colors.purple.withOpacity(0.7), Colors.purple.withOpacity(0.3)];
      case TransitionType.fadeblack:
        return [Colors.black.withOpacity(0.8), Colors.grey.withOpacity(0.4)];
      case TransitionType.fadewhite:
        return [Colors.white.withOpacity(0.8), Colors.grey.withOpacity(0.4)];
      case TransitionType.wipeleft:
      case TransitionType.wiperight:
      case TransitionType.wipeup:
      case TransitionType.wipedown:
        return [Colors.blue.withOpacity(0.7), Colors.blue.withOpacity(0.3)];
      case TransitionType.slideleft:
      case TransitionType.slideright:
      case TransitionType.slideup:
      case TransitionType.slidedown:
        return [Colors.green.withOpacity(0.7), Colors.green.withOpacity(0.3)];
      case TransitionType.zoomin:
        return [Colors.orange.withOpacity(0.7), Colors.orange.withOpacity(0.3)];
      default:
        return [Colors.grey.withOpacity(0.7), Colors.grey.withOpacity(0.3)];
    }
  }

  IconData _getTransitionIcon() {
    switch (transitionType) {
      case TransitionType.fade:
      case TransitionType.fadeblack:
      case TransitionType.fadewhite:
        return Icons.blur_on;
      case TransitionType.wipeleft:
        return Icons.keyboard_arrow_left;
      case TransitionType.wiperight:
        return Icons.keyboard_arrow_right;
      case TransitionType.wipeup:
        return Icons.keyboard_arrow_up;
      case TransitionType.wipedown:
        return Icons.keyboard_arrow_down;
      case TransitionType.slideleft:
        return Icons.arrow_back;
      case TransitionType.slideright:
        return Icons.arrow_forward;
      case TransitionType.slideup:
        return Icons.arrow_upward;
      case TransitionType.slidedown:
        return Icons.arrow_downward;
      case TransitionType.zoomin:
        return Icons.zoom_in;
      default:
        return Icons.animation;
    }
  }

  String _getTransitionLabel() {
    switch (transitionType) {
      case TransitionType.fade:
        return 'FADE';
      case TransitionType.fadeblack:
        return 'FADE\nBLACK';
      case TransitionType.fadewhite:
        return 'FADE\nWHITE';
      case TransitionType.wipeleft:
        return 'WIPE\nLEFT';
      case TransitionType.wiperight:
        return 'WIPE\nRIGHT';
      case TransitionType.wipeup:
        return 'WIPE\nUP';
      case TransitionType.wipedown:
        return 'WIPE\nDOWN';
      case TransitionType.slideleft:
        return 'SLIDE\nLEFT';
      case TransitionType.slideright:
        return 'SLIDE\nRIGHT';
      case TransitionType.slideup:
        return 'SLIDE\nUP';
      case TransitionType.slidedown:
        return 'SLIDE\nDOWN';
      case TransitionType.zoomin:
        return 'ZOOM\nIN';
      default:
        return transitionType.name.toUpperCase();
    }
  }

  Widget _buildTransitionPattern() {
    switch (transitionType) {
      case TransitionType.fade:
      case TransitionType.fadeblack:
      case TransitionType.fadewhite:
        return _buildFadePattern();
      case TransitionType.wipeleft:
      case TransitionType.wiperight:
        return _buildWipePattern();
      case TransitionType.slideleft:
      case TransitionType.slideright:
        return _buildSlidePattern();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildFadePattern() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.transparent,
            Colors.white.withOpacity(0.1),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }

  Widget _buildWipePattern() {
    return CustomPaint(
      painter: WipePatternPainter(transitionType),
      size: Size.infinite,
    );
  }

  Widget _buildSlidePattern() {
    return CustomPaint(
      painter: SlidePatternPainter(transitionType),
      size: Size.infinite,
    );
  }
}

/// Custom painter for wipe transition patterns
class WipePatternPainter extends CustomPainter {
  final TransitionType transitionType;

  WipePatternPainter(this.transitionType);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..strokeWidth = 1;

    const lineCount = 5;
    final spacing = size.height / lineCount;

    for (int i = 0; i < lineCount; i++) {
      final y = spacing * i;
      if (transitionType == TransitionType.wipeleft) {
        canvas.drawLine(
          Offset(0, y),
          Offset(size.width * (i / lineCount), y),
          paint,
        );
      } else {
        canvas.drawLine(
          Offset(size.width * (1 - i / lineCount), y),
          Offset(size.width, y),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for slide transition patterns
class SlidePatternPainter extends CustomPainter {
  final TransitionType transitionType;

  SlidePatternPainter(this.transitionType);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..strokeWidth = 2;

    final arrowSize = size.height * 0.3;
    final centerY = size.height / 2;

    if (transitionType == TransitionType.slideleft) {
      // Draw left arrow
      canvas.drawLine(
        Offset(size.width * 0.3, centerY),
        Offset(size.width * 0.7, centerY),
        paint,
      );
      canvas.drawLine(
        Offset(size.width * 0.3, centerY),
        Offset(size.width * 0.3 + arrowSize * 0.3, centerY - arrowSize * 0.2),
        paint,
      );
      canvas.drawLine(
        Offset(size.width * 0.3, centerY),
        Offset(size.width * 0.3 + arrowSize * 0.3, centerY + arrowSize * 0.2),
        paint,
      );
    } else {
      // Draw right arrow
      canvas.drawLine(
        Offset(size.width * 0.3, centerY),
        Offset(size.width * 0.7, centerY),
        paint,
      );
      canvas.drawLine(
        Offset(size.width * 0.7, centerY),
        Offset(size.width * 0.7 - arrowSize * 0.3, centerY - arrowSize * 0.2),
        paint,
      );
      canvas.drawLine(
        Offset(size.width * 0.7, centerY),
        Offset(size.width * 0.7 - arrowSize * 0.3, centerY + arrowSize * 0.2),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
