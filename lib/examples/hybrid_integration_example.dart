import 'dart:io';
import 'package:flutter/material.dart';
import '../screens/project/models/video_track_model.dart';
import '../screens/project/models/text_track_model.dart';
import '../screens/project/models/audio_track_model.dart';
import '../models/preprocessing_options.dart';
import '../services/hybrid_video_editor_mixin.dart';
import '../services/hybrid_video_export_service.dart';
import '../screens/project/new_editor/canvas/hybrid_crop_preview_widget.dart';
import '../controllers/video_controller.dart';

/// Example integration showing how to use the hybrid video editor system
/// This demonstrates the complete workflow from preprocessing to export
class HybridVideoEditorExample extends StatefulWidget {
  @override
  _HybridVideoEditorExampleState createState() => _HybridVideoEditorExampleState();
}

class _HybridVideoEditorExampleState extends State<HybridVideoEditorExample> 
    with HybridVideoEditorMixin {
  
  List<VideoTrackModel> _videoTracks = [];
  List<TextTrackModel> _textTracks = [];
  List<AudioTrackModel> _audioTracks = [];
  
  // Required by HybridVideoEditorMixin
  @override
  List<VideoTrackModel> get videoTracks => _videoTracks;
  
  @override
  Size get canvasSize => const Size(1920, 1080);
  
  @override
  void updateVideoTrack(int index, VideoTrackModel track) {
    setState(() {
      _videoTracks[index] = track;
    });
  }
  
  @override
  VideoEditorController? getVideoControllerForTrack(String trackId) {
    // Return your video controller for the track
    // Implementation depends on your current architecture
    return null; // TODO: Implement based on your current setup
  }
  
  @override
  Future<void> updateVideoControllerForTrack(String trackId, VideoEditorController newController) async {
    // Update your video controller mapping
    // Implementation depends on your current architecture
    // TODO: Implement based on your current setup
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hybrid Video Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.file_upload),
            onPressed: _importVideo,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportVideo,
          ),
        ],
      ),
      body: Column(
        children: [
          // Canvas preview area
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              color: Colors.black,
              child: _buildCanvasPreview(),
            ),
          ),
          
          // Controls area
          Expanded(
            flex: 1,
            child: _buildControls(),
          ),
          
          // Preprocessing status
          if (hasPreprocessingTracks) _buildPreprocessingStatus(),
        ],
      ),
    );
  }

  Widget _buildCanvasPreview() {
    if (_videoTracks.isEmpty) {
      return const Center(
        child: Text(
          'Import a video to get started',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    // Example with the first video track
    final track = _videoTracks.first;
    
    return HybridCropPreviewWidget(
      trackId: track.id,
      provider: this,
      videoSize: const Size(1920, 1080), // Get from video controller
      previewSize: const Size(400, 225),
      showCropOverlay: track.hasCrop,
      cropModel: track.canvasCropModel,
      onCropChanged: (cropModel) {
        // Update crop model
        final index = _videoTracks.indexWhere((t) => t.id == track.id);
        if (index != -1) {
          updateVideoTrack(index, track.copyWith(canvasCropModel: cropModel));
        }
      },
      onCropCompleted: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Crop applied successfully!')),
        );
      },
      onCropError: (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Crop error: $error')),
        );
      },
      child: Container(
        width: 400,
        height: 225,
        color: Colors.grey,
        child: const Center(
          child: Text('Video Preview', style: TextStyle(color: Colors.white)),
        ),
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Crop controls
          Row(
            children: [
              ElevatedButton(
                onPressed: _videoTracks.isNotEmpty ? _showCropDialog : null,
                child: const Text('Crop Video'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _videoTracks.isNotEmpty ? _showTrimDialog : null,
                child: const Text('Trim Video'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _videoTracks.isNotEmpty ? _showSpeedDialog : null,
                child: const Text('Speed'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Preprocessing controls
          Row(
            children: [
              ElevatedButton(
                onPressed: _videoTracks.isNotEmpty ? _showPreprocessingOptions : null,
                child: const Text('Preprocess'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: clearPreprocessingData,
                child: const Text('Clear Cache'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPreprocessingStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.orange.withValues(alpha: 0.1),
      child: Row(
        children: [
          CircularProgressIndicator(
            value: overallPreprocessingProgress,
            strokeWidth: 2,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  preprocessingStatusMessage,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Text(
                  'Using easy_video_editor for optimal quality',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Example: Import video
  void _importVideo() async {
    // This is a placeholder - implement with your file picker
    // For demonstration, creating a dummy video track
    final dummyFile = File('/path/to/video.mp4');
    
    if (await dummyFile.exists()) {
      final track = VideoTrackModel(
        originalFile: dummyFile,
        processedFile: dummyFile,
        totalDuration: 30000, // 30 seconds in milliseconds
        canvasPosition: Offset(100, 50),
        canvasSize: const Size(400, 225),
        canvasZIndex: _videoTracks.length,
      );
      
      setState(() {
        _videoTracks.add(track);
      });
    }
  }

  // Example: Show crop dialog
  void _showCropDialog() {
    if (_videoTracks.isEmpty) return;
    
    final track = _videoTracks.first;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Crop Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose crop method:'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _applyCropViaPreprocessing(track);
              },
              child: const Text('Smart Crop (easy_video_editor)'),
            ),
            const SizedBox(height: 8),
            OutlinedButton(
              onPressed: () {
                Navigator.pop(context);
                _applyCropViaCanvas(track);
              },
              child: const Text('Canvas Crop (FFmpeg)'),
            ),
          ],
        ),
      ),
    );
  }

  // Example: Apply crop via preprocessing
  void _applyCropViaPreprocessing(VideoTrackModel track) async {
    final cropModel = CropModel(
      x: 100,
      y: 100,
      width: 800,
      height: 600,
      enabled: true,
      sourceWidth: 1920,
      sourceHeight: 1080,
    );

    await applyCropToTrack(
      trackId: track.id,
      cropModel: cropModel,
    );
  }

  // Example: Apply crop via canvas
  void _applyCropViaCanvas(VideoTrackModel track) {
    final cropModel = CropModel(
      x: 100,
      y: 100,
      width: 800,
      height: 600,
      enabled: true,
      sourceWidth: 1920,
      sourceHeight: 1080,
    );

    final index = _videoTracks.indexWhere((t) => t.id == track.id);
    if (index != -1) {
      updateVideoTrack(index, track.copyWith(canvasCropModel: cropModel));
    }
  }

  // Example: Show trim dialog
  void _showTrimDialog() {
    if (_videoTracks.isEmpty) return;
    
    final track = _videoTracks.first;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Trim Video'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _applyTrimViaPreprocessing(track);
              },
              child: const Text('Trim (5s - 15s)'),
            ),
          ],
        ),
      ),
    );
  }

  // Example: Apply trim via preprocessing
  void _applyTrimViaPreprocessing(VideoTrackModel track) async {
    await applyTrimToTrack(
      trackId: track.id,
      startSeconds: 5.0,
      endSeconds: 15.0,
    );
  }

  // Example: Show speed dialog
  void _showSpeedDialog() {
    if (_videoTracks.isEmpty) return;
    
    final track = _videoTracks.first;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Speed Adjustment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _applySpeedViaPreprocessing(track, 2.0);
              },
              child: const Text('2x Speed'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _applySpeedViaPreprocessing(track, 0.5);
              },
              child: const Text('0.5x Speed'),
            ),
          ],
        ),
      ),
    );
  }

  // Example: Apply speed via preprocessing
  void _applySpeedViaPreprocessing(VideoTrackModel track, double speed) async {
    await applySpeedToTrack(
      trackId: track.id,
      speedFactor: speed,
    );
  }

  // Example: Show preprocessing options dialog
  void _showPreprocessingOptions() {
    if (_videoTracks.isEmpty) return;
    
    final track = _videoTracks.first;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Preprocessing Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Compress Video'),
              onTap: () {
                Navigator.pop(context);
                _applyCompression(track);
              },
            ),
            ListTile(
              title: const Text('Rotate 90°'),
              onTap: () {
                Navigator.pop(context);
                _applyRotation(track);
              },
            ),
            ListTile(
              title: const Text('Mute Audio'),
              onTap: () {
                Navigator.pop(context);
                _muteAudio(track);
              },
            ),
          ],
        ),
      ),
    );
  }

  // Example: Apply compression
  void _applyCompression(VideoTrackModel track) async {
    await applyCompressionToTrack(
      trackId: track.id,
      quality: VideoQuality.high,
      resolution: '1280x720',
    );
  }

  // Example: Apply rotation
  void _applyRotation(VideoTrackModel track) async {
    await applyRotationToTrack(
      trackId: track.id,
      degrees: 90,
    );
  }

  // Example: Mute audio
  void _muteAudio(VideoTrackModel track) async {
    await applyAudioProcessingToTrack(
      trackId: track.id,
      mute: true,
    );
  }

  // Example: Export video
  void _exportVideo() async {
    if (_videoTracks.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No videos to export')),
      );
      return;
    }

    // Validate export configuration
    final validation = HybridVideoExportService.validateExportConfig(
      videoTracks: _videoTracks,
      textTracks: _textTracks,
      audioTracks: _audioTracks,
      canvasSize: canvasSize,
    );

    if (!validation['isValid']) {
      _showExportValidationDialog(validation);
      return;
    }

    // Generate export command
    final outputPath = '/path/to/output.mp4';
    final command = HybridVideoExportService.generateOptimizedFFmpegCommand(
      videoTracks: _videoTracks,
      textTracks: _textTracks,
      audioTracks: _audioTracks,
      canvasSize: canvasSize,
      outputPath: outputPath,
      fastStart: true,
      useHardwareAcceleration: true,
    );

    // Get export summary
    final summary = HybridVideoExportService.getExportSummary(
      videoTracks: _videoTracks,
      textTracks: _textTracks,
      audioTracks: _audioTracks,
    );

    _showExportSummaryDialog(summary, command);
  }

  void _showExportValidationDialog(Map<String, dynamic> validation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Issues'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (validation['issues'].isNotEmpty) ...[
              const Text('Issues:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
              ...validation['issues'].map<Widget>((issue) => Text('• $issue')),
              const SizedBox(height: 16),
            ],
            if (validation['warnings'].isNotEmpty) ...[
              const Text('Warnings:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange)),
              ...validation['warnings'].map<Widget>((warning) => Text('• $warning')),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showExportSummaryDialog(Map<String, dynamic> summary, String command) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Summary'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Strategy: ${summary['processingStrategy']}'),
              Text('Video Tracks: ${summary['totalVideoTracks']}'),
              Text('Preprocessed: ${summary['preprocessedTracks']}'),
              if (summary['spaceSavingsPercent'] > 0)
                Text('Space Savings: ${summary['spaceSavingsPercent'].toStringAsFixed(1)}%'),
              const SizedBox(height: 16),
              const Text('Benefits:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...summary['benefits'].map<Widget>((benefit) => Text('• $benefit')),
              const SizedBox(height: 16),
              const Text('FFmpeg Command:', style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                padding: const EdgeInsets.all(8),
                color: Colors.grey[100],
                child: Text(
                  command,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _executeExport(command);
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _executeExport(String command) {
    // Execute the FFmpeg command
    // Implementation depends on your FFmpeg execution setup
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export started...')),
    );
  }
}